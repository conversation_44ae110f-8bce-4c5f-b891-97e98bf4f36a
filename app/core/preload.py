"""
预加载模块，在应用程序启动时初始化资源
"""
from utils.logger import get_logger

logs = get_logger()

def init_resources():
    """初始化应用程序资源"""
    logs.info("Initializing application resources...")

    # 初始化HTTP客户端
    try:
        from ..core.http_client import get_http_client, get_async_http_client

        # 初始化HTTP客户端
        http_client = get_http_client()
        async_http_client = get_async_http_client()
        logs.info("HTTP clients initialized")
    except Exception as e:
        logs.error(f"Failed to initialize HTTP clients: {str(e)}")

    # 初始化缓存
    try:
        from ..core.cache import get_cache

        # 获取缓存实例
        cache = get_cache()
        logs.info("Cache system initialized")
    except Exception as e:
        logs.error(f"Failed to initialize cache: {str(e)}")

    # 初始化Supabase单例客户端
    try:
        from ..db.supabase_singleton import get_supabase, get_supabase_stats

        # 初始化Supabase客户端
        supabase = get_supabase()
        logs.info("Supabase singleton client initialized")
    except Exception as e:
        logs.error(f"Failed to initialize Supabase singleton client: {str(e)}")

    logs.info("Application resources initialized successfully")

# 在导入时自动初始化资源
init_resources()

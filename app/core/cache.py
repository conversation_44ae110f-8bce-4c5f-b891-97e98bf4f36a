import time
from typing import Any, Dict, Optional, Callable, TypeVar, Generic
from functools import wraps

from app.api.auth import logs

T = TypeVar('T')


class CacheItem(Generic[T]):
    """缓存项"""
    def __init__(self, value: T, ttl: int = 60):
        self.value = value
        self.expires_at = time.time() + ttl
    
    @property
    def is_expired(self) -> bool:
        """检查缓存项是否已过期"""
        return time.time() > self.expires_at


class SimpleCache:
    """简单的内存缓存实现"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SimpleCache, cls).__new__(cls)
            cls._instance._cache = {}
        return cls._instance
    
    def __init__(self):
        # 已在 __new__ 中初始化
        pass
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项，如果不存在或已过期则返回None"""
        if key not in self._cache:
            return None
        
        cache_item = self._cache[key]
        if cache_item.is_expired:
            # 自动清理过期项
            del self._cache[key]
            return None
        
        return cache_item.value
    
    def set(self, key: str, value: Any, ttl: int = 60) -> None:
        """设置缓存项，ttl为过期时间（秒）"""
        self._cache[key] = CacheItem(value, ttl)
    
    def delete(self, key: str) -> None:
        """删除缓存项"""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
    
    def cleanup(self) -> None:
        """清理所有过期的缓存项"""
        now = time.time()
        expired_keys = [
            key for key, item in self._cache.items() 
            if now > item.expires_at
        ]
        for key in expired_keys:
            del self._cache[key]


# 创建缓存装饰器
def cached(ttl: int = 60, key_prefix: str = ""):
    """
    缓存装饰器，用于缓存函数返回值
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
    """
    cache = SimpleCache()
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    
    return decorator


# 为特定用户ID缓存数据的装饰器
def user_cached(ttl: int = 60):
    """
    用户数据缓存装饰器，根据用户ID缓存数据
    
    Args:
        ttl: 缓存过期时间（秒）
    """
    cache = SimpleCache()
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 查找用户ID参数
            user_id = None
            for arg in args:
                if hasattr(arg, 'id'):  # 假设用户对象有id属性
                    user_id = arg.id
                    break
            
            if not user_id and 'current_user' in kwargs:
                user_id = kwargs['current_user'].id
            
            if not user_id:
                # 如果找不到用户ID，则不缓存
                return await func(*args, **kwargs)
            
            # 生成缓存键
            cache_key = f"user:{user_id}:{func.__name__}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            logs.info(f"cached_result: {cached_result}")
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    
    return decorator


# 获取缓存实例
def get_cache() -> SimpleCache:
    """获取缓存实例"""
    return SimpleCache()

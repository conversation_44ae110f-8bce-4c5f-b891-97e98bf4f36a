import os
from typing import Optional, Callable, Tuple
import time
import uuid
import alibabacloud_oss_v2 as oss
import mimetypes
import threading

from app.core import aa_bb
from utils.logger import get_logger

logger = get_logger()





class AliyunOSSClientV2:
    """阿里云OSS客户端 (使用SDK V2)"""

    def __init__(self, access_key_id: str, access_key_secret: str, region: str, bucket_name: str):
        """
        初始化OSS客户端

        Args:
            access_key_id: 阿里云访问密钥ID
            access_key_secret: 阿里云访问密钥Secret
            region: OSS区域，如 'cn-beijing'
            bucket_name: OSS存储桶名称
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.region = region
        self.bucket_name = bucket_name

        # 创建认证提供者
        self.credentials_provider = oss.credentials.StaticCredentialsProvider(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )

        # 创建OSS客户端配置
        self.cfg = oss.config.load_default()
        self.cfg.credentials_provider = self.credentials_provider
        self.cfg.region = region

        # 创建OSS客户端
        self.client = oss.Client(self.cfg)

    def upload_file(self,
                    local_file_path: str,
                    oss_path: Optional[str] = None,
                    progress_callback: Optional[Callable[[int], None]] = None
                    ) -> Tuple[bool, str, str]:
        """
        上传文件到OSS

        Args:
            local_file_path: 本地文件路径
            oss_path: OSS上的文件路径，如果不指定，将自动生成
            progress_callback: 进度回调函数，参数为上传进度（0-100）

        Returns:
            Tuple[bool, str, str]: (是否成功, OSS文件路径, 错误信息)
        """
        logger.info(f"正在上传文件到OSS: {local_file_path}")

        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            error_msg = f"文件不存在: {local_file_path}"
            logger.error(error_msg)
            return False, "", error_msg

        try:
            # 如果没有指定OSS路径，则自动生成
            if not oss_path:
                file_ext = os.path.splitext(local_file_path)[1]
                timestamp = int(time.time())
                random_str = str(uuid.uuid4()).replace("-", "")[:8]
                oss_path = f"audio/{timestamp}_{random_str}{file_ext}"

            # 获取文件的MIME类型
            content_type, _ = mimetypes.guess_type(local_file_path)
            if not content_type:
                content_type = 'application/octet-stream'

            # 设置元数据
            metadata = {
                "Content-Type": content_type
            }

            # 上传文件
            with open(local_file_path, 'rb') as file_obj:
                data = file_obj.read()

                # 如果有进度回调，模拟进度
                if progress_callback:
                    progress_callback(10)  # 开始上传

                # 执行上传
                result = self.client.put_object(oss.PutObjectRequest(
                    bucket=self.bucket_name,
                    key=oss_path,
                    body=data,
                    metadata=metadata
                ))

                if progress_callback:
                    progress_callback(100)  # 上传完成

                if result.status_code == 200:
                    logger.info(f"文件上传成功: {oss_path}")
                    return True, oss_path, ""
                else:
                    error_msg = f"上传失败，状态码: {result.status_code}"
                    logger.error(error_msg)
                    return False, "", error_msg

        except Exception as e:
            error_msg = f"上传文件时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def generate_url(self, oss_path: str, expires: int = 3600) -> str:
        """
        生成文件的访问链接

        Args:
            oss_path: OSS上的文件路径
            expires: 链接的有效期（秒），默认为1小时

        Returns:
            str: 访问链接URL

        Raises:
            Exception: 生成URL时发生错误
        """
        if not oss_path:
            error_msg = "生成URL失败: OSS路径不能为空"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            return self._generate_url(oss_path, expires)
        except Exception as e:
            error_msg = f"生成文件访问链接时发生错误: {str(e)}"
            logger.error(error_msg)
            raise ValueError(e) from e

    def _generate_url(self, oss_path, expires):
        """
        生成文件的访问链接

        Args:
            oss_path: oss文件路径
            expires: 有效时间

        Returns: 签名文件路径

        """
        logger.info("开始生成文件的访问链接")

        pre_result = self.client.presign(
            oss.GetObjectRequest(
                bucket=self.bucket_name,  # 指定存储空间名称
                key=oss_path,  # 指定对象键名
                expires=expires  # 设置链接有效期
            )

        )

        # 打印预签名请求的详细信息
        logger.info("生成预签名请求成功:")
        logger.info(f"  - URL: {pre_result.url}")

        return pre_result.url

    def upload_and_generate_url(self,
                                local_file_path: str,
                                oss_path: Optional[str] = None,
                                expires: int = 24 * 3600,  # 默认24小时
                                progress_callback: Optional[Callable[[int], None]] = None
                                ) -> Tuple[bool, str, str]:
        """
        上传文件并生成访问链接

        Args:
            local_file_path: 本地文件路径
            oss_path: OSS上的文件路径，如果不指定，将自动生成
            expires: 链接的有效期（秒），默认为24小时
            progress_callback: 进度回调函数，参数为上传进度（0-100）

        Returns:
            Tuple[bool, str, str]: (是否成功, 访问链接, 错误信息)
        """
        # 记录开始时间
        start_time = time.time()

        # 检查文件路径
        if not local_file_path:
            error_msg = "上传文件失败: 本地文件路径不能为空"
            logger.error(error_msg)
            return False, "", error_msg

        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            error_msg = f"上传文件失败: 文件不存在: {local_file_path}"
            logger.error(error_msg)
            return False, "", error_msg

        try:
            # 记录文件大小
            file_size = os.path.getsize(local_file_path)
            file_name = os.path.basename(local_file_path)
            logger.info(f"开始上传文件: {file_name}, 大小: {file_size / 1024 / 1024:.2f}MB")

            # 上传文件
            success, oss_path, error = self.upload_file(local_file_path, oss_path, progress_callback)
            if not success:
                return False, "", error

            logger.info(f"文件上传成功: {oss_path}")

            try:
                # 生成访问链接
                url = self.generate_url(oss_path, expires)

                # 记录结束时间和耗时
                end_time = time.time()
                elapsed_time = end_time - start_time
                logger.info(f"上传文件并生成URL完成，耗时: {elapsed_time:.2f}秒")

                return True, url, ""
            except Exception as e:
                error_msg = f"生成URL时发生错误: {str(e)}"
                logger.error(error_msg)
                return False, "", error_msg

        except Exception as e:
            error_msg = f"上传文件过程中发生错误: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def list_objects(self, 
                    prefix: Optional[str] = None,
                    delimiter: Optional[str] = None,
                    max_keys: int = 100,
                    marker: Optional[str] = None) -> Tuple[bool, list, str]:
        """
        列举OSS上的文件

        Args:
            prefix: 文件前缀，用于筛选特定目录下的文件
            delimiter: 分隔符，用于模拟目录结构
            max_keys: 返回的最大文件数量
            marker: 分页标记，用于获取下一页

        Returns:
            Tuple[bool, list, str]: (是否成功, 文件列表, 错误信息)
            文件列表中的每个元素包含：
            {
                'key': str,  # 文件路径
                'size': int,  # 文件大小
                'last_modified': datetime,  # 最后修改时间
                'etag': str,  # 文件ETag
                'type': str,  # 文件类型
                'storage_class': str  # 存储类型
            }
        """
        try:
            # 构建请求参数
            request = oss.ListObjectsRequest(
                bucket=self.bucket_name,
                prefix=prefix,
                delimiter=delimiter,
                max_keys=max_keys,
                marker=marker
            )

            # 发送请求
            result = self.client.list_objects(request)

            # 处理结果
            files = []
            for obj in result.contents:
                file_info = {
                    'key': obj.key,
                    'size': obj.size,
                    'last_modified': obj.last_modified,
                    'etag': obj.etag,
                    'type': obj.type,
                    'storage_class': obj.storage_class
                }
                files.append(file_info)

            # 如果有公共前缀（目录），也添加到结果中
            if hasattr(result, 'common_prefixes'):
                for prefix in result.common_prefixes:
                    files.append({
                        'key': prefix.prefix,
                        'type': 'directory',
                        'size': 0,
                        'last_modified': None,
                        'etag': None,
                        'storage_class': None
                    })

            return True, files, ""

        except Exception as e:
            error_msg = f"列举文件时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, [], error_msg

    def list_objects_recursive(self,
                             prefix: Optional[str] = None,
                             max_keys: int = 1000) -> Tuple[bool, list, str]:
        """
        递归列举OSS上的所有文件

        Args:
            prefix: 文件前缀，用于筛选特定目录下的文件
            max_keys: 每次请求的最大文件数量

        Returns:
            Tuple[bool, list, str]: (是否成功, 文件列表, 错误信息)
        """
        try:
            all_files = []
            marker = None

            while True:
                # 获取一页文件
                success, files, error = self.list_objects(
                    prefix=prefix,
                    max_keys=max_keys,
                    marker=marker
                )

                if not success:
                    return False, [], error

                # 添加文件到结果列表
                all_files.extend(files)

                # 如果没有更多文件，退出循环
                if len(files) < max_keys:
                    break

                # 更新marker用于获取下一页
                marker = files[-1]['key']

            return True, all_files, ""

        except Exception as e:
            error_msg = f"递归列举文件时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, [], error_msg

    def generate_download_url(self, oss_path: str, expires: int = 86400) -> Tuple[bool, str, str]:
        """
        生成软件下载链接（预签名URL）

        Args:
            oss_path: OSS上的文件路径
            expires: 链接的有效期（秒），默认24小时

        Returns:
            Tuple[bool, str, str]: (是否成功, 下载链接, 错误信息)
        """
        try:
            # 检查文件是否存在
            result = self.client.is_object_exist(
                bucket=self.bucket_name,
                key=oss_path
            )

            if not result:
                return False, "", f"文件不存在: {oss_path}"

            # 生成预签名URL
            pre_result = self.client.presign(
                oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key=oss_path,
                    expires=expires
                )
            )

            return True, pre_result.url, ""

        except Exception as e:
            error_msg = f"生成下载链接时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def get_latest_download_url(self) -> Tuple[bool, str, str]:
        """
        获取最新的软件下载链接

        Returns:
            Tuple[bool, str, str]: (是否成功, 下载链接, 错误信息)
        """
        try:

            # 生成预签名URL
            pre_result = self.client.presign(
                oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key="client/Lapped AI setup.exe",
                    expires=86400  # 24小时
                )
            )

            return True, pre_result.url, ""

        except Exception as e:
            error_msg = f"获取下载链接时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg


class OSSClientSingleton:
    """OSS客户端单例类"""
    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(OSSClientSingleton, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        with self._lock:
            if self._initialized:
                return

            self._initialized = True
            self._client = None
            self._config = aa_bb
            self._initialize()

    def _initialize(self):
        """初始化OSS客户端"""
        try:
            self._client = AliyunOSSClientV2(
                access_key_id=self._config.aki,
                access_key_secret=self._config.aks,
                region=self._config.region,
                bucket_name=self._config.bucket
            )
            logger.info("OSS client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OSS client: {str(e)}")
            raise


    @property
    def client(self):
        """获取OSS客户端实例"""
        return self._client


# 创建全局单例实例
oss_client = OSSClientSingleton().client

if __name__ == '__main__':
    # 示例：生成软件下载链接
    # success, download_url, error = oss_client.generate_download_url(
    #     oss_path="client/Lapped AI setup.exe",  # 软件在OSS中的路径
    #     expires=86400  # 链接24小时后过期
    # )
    #
    # if success:
    #     print(f"下载链接: {download_url}")
    #     print("链接将在24小时后过期")
    # else:
    #     print(f"生成下载链接失败: {error}")

    # 测试获取最新下载链接
    success, download_url, error = oss_client.get_latest_download_url()

    if success:
        print(f"下载链接: {download_url}")
        print("链接将在24小时后过期")
    else:
        print(f"获取下载链接失败: {error}")
"""
HTTP客户端连接池模块
提供优化的HTTP客户端，用于所有外部API调用
"""
import os
import time
import asyncio
import httpx
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from utils.logger import get_logger

logs = get_logger()

# HTTP客户端配置
HTTP_TIMEOUT = int(os.getenv("HTTP_TIMEOUT", "30"))  # 请求超时时间（秒）
HTTP_POOL_CONNECTIONS = int(os.getenv("HTTP_POOL_CONNECTIONS", "20"))  # 连接池大小
HTTP_POOL_MAXSIZE = int(os.getenv("HTTP_POOL_MAXSIZE", "100"))  # 最大连接数
HTTP_MAX_KEEPALIVE = int(os.getenv("HTTP_MAX_KEEPALIVE", "5"))  # 连接保持时间（秒）
HTTP_RETRY_COUNT = int(os.getenv("HTTP_RETRY_COUNT", "3"))  # 重试次数

# 全局HTTP客户端
_http_client = None
_async_http_client = None

def get_http_client() -> httpx.Client:
    """
    获取同步HTTP客户端实例（单例模式）
    
    Returns:
        httpx.Client: HTTP客户端实例
    """
    global _http_client
    
    if _http_client is None:
        # 创建带有连接池的HTTP客户端
        limits = httpx.Limits(
            max_connections=HTTP_POOL_CONNECTIONS,
            max_keepalive_connections=HTTP_POOL_MAXSIZE,
            keepalive_expiry=HTTP_MAX_KEEPALIVE
        )
        
        _http_client = httpx.Client(
            timeout=HTTP_TIMEOUT,
            limits=limits,
            http2=True,  # 启用HTTP/2
            follow_redirects=True
        )
        
        logs.info(f"HTTP client initialized with pool size: {HTTP_POOL_CONNECTIONS}")
    
    return _http_client

def get_async_http_client() -> httpx.AsyncClient:
    """
    获取异步HTTP客户端实例（单例模式）
    
    Returns:
        httpx.AsyncClient: 异步HTTP客户端实例
    """
    global _async_http_client
    
    if _async_http_client is None:
        # 创建带有连接池的异步HTTP客户端
        limits = httpx.Limits(
            max_connections=HTTP_POOL_CONNECTIONS,
            max_keepalive_connections=HTTP_POOL_MAXSIZE,
            keepalive_expiry=HTTP_MAX_KEEPALIVE
        )
        
        _async_http_client = httpx.AsyncClient(
            timeout=HTTP_TIMEOUT,
            limits=limits,
            http2=True,  # 启用HTTP/2
            follow_redirects=True
        )
        
        logs.info(f"Async HTTP client initialized with pool size: {HTTP_POOL_CONNECTIONS}")
    
    return _async_http_client

@asynccontextmanager
async def get_http_session():
    """
    获取HTTP会话的异步上下文管理器
    
    使用方式:
    ```
    async with get_http_session() as client:
        response = await client.get("https://example.com")
    ```
    """
    client = get_async_http_client()
    try:
        yield client
    except Exception as e:
        logs.error(f"HTTP client error: {str(e)}")
        raise

async def close_http_clients():
    """关闭HTTP客户端，释放资源"""
    global _http_client, _async_http_client
    
    if _http_client is not None:
        _http_client.close()
        _http_client = None
        logs.info("HTTP client closed")
    
    if _async_http_client is not None:
        await _async_http_client.aclose()
        _async_http_client = None
        logs.info("Async HTTP client closed")

async def request_with_retry(
    method: str,
    url: str,
    retry_count: int = HTTP_RETRY_COUNT,
    **kwargs
) -> httpx.Response:
    """
    带重试机制的HTTP请求
    
    Args:
        method: HTTP方法（GET, POST等）
        url: 请求URL
        retry_count: 重试次数
        **kwargs: 传递给httpx的其他参数
    
    Returns:
        httpx.Response: HTTP响应
    """
    client = get_async_http_client()
    last_error = None
    
    for attempt in range(retry_count + 1):
        try:
            start_time = time.time()
            response = await getattr(client, method.lower())(url, **kwargs)
            
            # 记录请求时间
            request_time = (time.time() - start_time) * 1000
            if request_time > 500:  # 如果请求时间超过500ms，记录警告
                logs.warning(f"Slow HTTP request: {method} {url} - {request_time:.2f}ms")
            
            # 检查响应状态码
            if response.status_code >= 500 and attempt < retry_count:
                logs.warning(f"Server error {response.status_code} on attempt {attempt+1}/{retry_count+1} for {method} {url}")
                await asyncio.sleep(2 ** attempt)  # 指数退避
                continue
            
            return response
        
        except (httpx.ConnectError, httpx.ReadTimeout, httpx.WriteTimeout) as e:
            last_error = e
            if attempt < retry_count:
                logs.warning(f"Connection error on attempt {attempt+1}/{retry_count+1} for {method} {url}: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # 指数退避
            else:
                logs.error(f"Failed after {retry_count+1} attempts for {method} {url}: {str(e)}")
    
    # 如果所有重试都失败，抛出最后一个错误
    raise last_error

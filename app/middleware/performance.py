import time
import json
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from utils.logger import get_logger


logs = get_logger()

# 不需要记录详细日志的路径
EXCLUDE_LOGGING_PATHS = [
    "/api/transactions/get_balance",
    "/api/transactions/history",
    "/api/transactions/statistics"
]


class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    中间件，用于记录API请求的执行时间
    """

    async def dispatch(self, request: Request, call_next):
        # 记录请求开始时间
        start_time = time.time()
        request_id = f"{int(start_time * 1000)}"  # 生成请求ID

        # 获取请求路径和方法
        path = request.url.path
        method = request.method

        # 判断是否需要详细日志
        need_detailed_log = path not in EXCLUDE_LOGGING_PATHS

        # 获取查询参数（仅在需要详细日志时）
        query_params_str = ""
        if need_detailed_log:
            query_params = dict(request.query_params)
            query_params_str = json.dumps(query_params) if query_params else ""

        # 记录请求体（只对特定请求方法且在调试模式下）
        request_body = ""
        if settings.DEBUG and need_detailed_log and request.method in ["POST", "PUT", "PATCH"]:
            try:
                # 复制请求体，因为请求体只能读取一次
                body_bytes = await request.body()
                # 重新设置请求体，以便后续处理可以读取
                request._body = body_bytes

                if body_bytes:
                    try:
                        # 尝试解析为JSON
                        body_json = json.loads(body_bytes)
                        request_body = json.dumps(body_json, ensure_ascii=False)
                    except:
                        # 如果不是JSON，就将其转换为字符串
                        request_body = body_bytes.decode()
                        # 如果请求体过长，则截断
                        if len(request_body) > 1000:
                            request_body = request_body[:1000] + "..."
            except Exception as e:
                request_body = f"<无法读取请求体: {str(e)}>"

        # 记录请求开始（仅在需要详细日志时）
        if need_detailed_log:
            log_message = f"API请求开始 | ID: {request_id} | {method} {path}"
            if query_params_str:
                log_message += f" | 参数: {query_params_str}"
            if request_body:
                log_message += f" | 请求体: {request_body}"
            logs.info(log_message)

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间（毫秒）
            process_time = (time.time() - start_time) * 1000

            # 获取状态码
            status_code = response.status_code

            # 记录日志（根据路径决定日志级别）
            log_message = f"API请求完成 | ID: {request_id} | {method} {path} | 状态: {status_code} | 耗时: {process_time:.2f}ms"
            if need_detailed_log or process_time > 500:  # 如果处理时间超过500ms，即使是排除路径也记录
                logs.info(log_message)

            # 添加响应头，记录处理时间
            response.headers["X-Process-Time"] = f"{process_time:.2f}ms"
            response.headers["X-Request-ID"] = request_id

            return response

        except Exception as e:
            # 计算处理时间（毫秒）
            process_time = (time.time() - start_time) * 1000

            # 记录错误日志
            logs.error(f"API请求异常 | ID: {request_id} | {method} {path} | 耗时: {process_time:.2f}ms | 错误: {str(e)}")

            # 重新抛出异常
            raise

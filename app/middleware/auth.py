from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import lru_cache
from typing import Optional, Dict
import time

from ..db.supabase_singleton import supabase
from utils.logger import get_logger

logs = get_logger()

security = HTTPBearer()

# 不需要验证的路径
EXCLUDE_PATHS = [
    "/api/auth/login",
    "/api/auth/signup",
    "/api/auth/reset-password",
    "/api/auth/confirm-password-reset",
    "/api/auth/logout",
    "/api/client/check-version",  # 客户端版本检查接口
    "/api/client/add-version",  # 客户端版本检查接口
    "/api/client/tt",  # 客户端版本检查接口
    "/api/client/download/",  # 文件下载接口
    "/api/client/list-files",  # 列出可下载文件接口
    "/api/client/latest-file/",  # 列出可下载文件接口
    "/api/client/link"
    # "/docs",  # Swagger UI
    # "/redoc",  # ReDoc UI
    # "/openapi.json",  # OpenAPI schema
]

# Token 缓存配置
TOKEN_CACHE_TTL = 300  # 5分钟缓存
token_cache: Dict[str, tuple] = {}

def get_cached_user(token: str) -> Optional[dict]:
    """从缓存中获取用户信息"""
    if token in token_cache:
        user_data, timestamp = token_cache[token]
        if time.time() - timestamp < TOKEN_CACHE_TTL:
            return user_data
        del token_cache[token]
    return None

def cache_user(token: str, user_data: dict):
    """缓存用户信息"""
    token_cache[token] = (user_data, time.time())

async def verify_token(credentials: HTTPAuthorizationCredentials = None):
    """验证令牌并返回用户

    Args:
        credentials: 包含令牌的HTTP认证凭据

    Returns:
        已认证的用户对象

    Raises:
        HTTPException: 如果令牌缺失或无效
    """
    if not credentials:
        raise HTTPException(status_code=401, detail="未提供认证信息")

    token = credentials.credentials

    # 检查缓存
    cached_user = get_cached_user(token)
    if cached_user:
        return cached_user

    try:
        # 验证token
        result = supabase.auth.get_user(token)
        user_data = result.user

        # 缓存结果
        cache_user(token, user_data)
        return user_data
    except Exception as e:
        logs.error(f"Token validation error: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")


async def auth_middleware(request: Request, call_next):
    """Authentication middleware for FastAPI

    This middleware validates the JWT token in the Authorization header
    for all routes except those in EXCLUDE_PATHS.

    Args:
        request: The incoming request
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler
    """
    # 处理 OPTIONS 请求
    if request.method == "OPTIONS":
        return await call_next(request)

    # 跳过不需要认证的路径
    path = request.url.path
    # 检查路径是否在排除列表中，或者是否以排除列表中的前缀开头
    if path in EXCLUDE_PATHS or any(path.startswith(prefix) for prefix in EXCLUDE_PATHS if prefix.endswith('/')):
        return await call_next(request)

    try:
        # 获取认证头
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise HTTPException(
                status_code=401,
                detail="Missing Authorization header"
            )

        # 验证token格式
        try:
            scheme, token = auth_header.split()
        except ValueError:
            raise HTTPException(
                status_code=401,
                detail="Invalid authorization header format"
            )

        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication scheme"
            )

        # 使用缓存的验证函数
        user = await verify_token(HTTPAuthorizationCredentials(scheme=scheme, credentials=token))
        request.state.user = user

        response = await call_next(request)
        return response

    except HTTPException as http_error:
        logs.error(f"HTTP Exception: {str(http_error)}")
        # 创建一个新的响应，保留原始状态码和详细信息
        from fastapi.responses import JSONResponse
        return JSONResponse(
            status_code=http_error.status_code,
            content={"detail": http_error.detail}
        )
    except Exception as e:
        logs.error(f"Auth middleware error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal Server Error"
        )

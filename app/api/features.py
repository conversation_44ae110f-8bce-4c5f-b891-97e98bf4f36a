from fastapi import APIRouter, HTTPException

from utils.logger import get_logger
from ..db.supabase_singleton import supabase
from ..models.schemas import ResponseModel, RechargePackageResponse, TokenCoefficientSimpleResponse

logs = get_logger()

router = APIRouter()

@router.get("/recharge-packages", response_model=RechargePackageResponse)
async def get_recharge_packages():
    """获取所有可用的充值套餐，返回价格和算力数量"""
    try:
        response = supabase.table('token_exchange_rates').select('price,token_amount') \
        .eq('status', 1) \
        .order('sort_order', desc=False) \
        .execute()

        return RechargePackageResponse(
            message="success",
            data=response.data
    )
    except Exception as e:
        logs.error(f"Failed to get recharge packages: {str(e)}", exc_info=True)
        raise HTTPException(status_code=400, detail="获取充值套餐失败")


@router.get("/coefficients", response_model=TokenCoefficientSimpleResponse)
async def get_token_coefficients_simple():
    """获取所有启用的代币消耗系数的key和值"""
    try:
        response = supabase.table('token_coefficients')\
            .select('coefficient_key,coefficient_value')\
            .eq('status', 1)\
            .execute()

        return TokenCoefficientSimpleResponse(
            message="success",
            data=response.data
        )
    except Exception as e:
        logs.error(f"Failed to get token coefficients: {str(e)}", exc_info=True)
        raise HTTPException(status_code=400, detail="获取代币消耗系数失败")

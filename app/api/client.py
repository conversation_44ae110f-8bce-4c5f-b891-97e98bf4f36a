"""
客户端相关API路由
"""
import os
import io
from pathlib import Path
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Request, Depends
from fastapi.responses import FileResponse, StreamingResponse

from utils.logger import get_logger
from utils.version import compare_versions
from ..db.supabase_singleton import supabase
from ..core.oss import oss_client
from ..models.schemas import (
    ResponseModel, ClientVersionCheckRequest, ClientVersionAddRequest,
    VersionCheckResult, ErrorResponse, ResponseJson, FileInfo, FileListResponse
)

logs = get_logger()
router = APIRouter()


@router.post("/check-version", response_model=None, include_in_schema=True)
async def check_client_version(request: ClientVersionCheckRequest):
    """
    检查客户端版本是否需要更新

    Args:
        request: 包含平台和当前版本的请求对象

    Returns:
        版本检查结果，包括是否需要更新、更新URL等信息
    """
    try:
        logs.info(f"Checking version for platform: {request.platform}, current version: {request.current_version}")

        # 1. 获取平台配置信息
        platform = request.platform  # 获取枚举的字符串值
        platform_config_response = supabase.table('platform_configs') \
            .select('*') \
            .eq('platform', platform) \
            .execute()
        if not platform_config_response.data:
            logs.error(f"No platform configuration found for: {request.platform}")
            error_response = ErrorResponse(
                status="PLATFORM_CONFIG_NOT_FOUND",
                detail=f"未找到平台 {platform} 的配置信息"
            )
            return ResponseJson(
                code=200,
                message="error",
                data=error_response
            )

        platform_config = platform_config_response.data[0]
        logs.info(f'platform_config:{platform_config}')

        # 2. 获取最新版本信息
        latest_version_response = supabase.table('client_versions') \
            .select('*') \
            .eq('platform', platform) \
            .eq('status', 1) \
            .execute()

        if not latest_version_response.data:
            logs.error(f"No latest version found for platform: {request.platform}")
            error_response = ErrorResponse(
                status="VERSION_NOT_FOUND",
                detail=f"未找到平台 {platform} 的最新版本信息"
            )
            return ResponseJson(
                code=200,
                message="error",
                data=error_response
            )

        version_info = latest_version_response.data[0]

        # 3. 检查版本
        is_latest = compare_versions(request.current_version, platform_config['latest_version']) >= 0
        force_update_min = compare_versions(request.current_version, platform_config['min_version']) < 0

        # 4. 确定是否需要更新
        # 如果全局强制更新开启，或低于最低版本，或特定版本设置了强制更新，则强制更新
        force_update = platform_config['global_force_update'] or force_update_min or version_info['is_force_update']

        # 如果不是最新版本或需要强制更新，则需要更新
        need_update = not is_latest or force_update

        result = VersionCheckResult(
            is_latest=is_latest,
            need_update=need_update,
            force_update=force_update,
            latest_version=version_info['version'],
            update_url=version_info['update_url'],
            release_notes=version_info['release_notes']
        )

        logs.info(f"Version check result: {result}")
        return ResponseJson(
            code=200,
            message="success",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        logs.error(f"Failed to check client version: {str(e)}", exc_info=True)
        error_response = ErrorResponse(
            status="VERSION_CHECK_FAILED",
            detail="检查客户端版本失败"
        )
        return ResponseJson(
            code=200,
            message="error",
            data=error_response
        )


@router.post("/add-version", response_model=None, include_in_schema=True)
async def add_client_version(request: ClientVersionAddRequest):
    """
    添加新的客户端版本

    Args:
        request: 包含平台、版本号、更新地址、更新说明等信息的请求对象

    Returns:
        添加结果
    """
    try:
        logs.info(f"Adding new version for platform: {request.platform}, version: {request.version}")

        # 1. 检查版本是否已存在
        existing_version = supabase.table('client_versions') \
            .select('*') \
            .eq('platform', request.platform) \
            .eq('version', request.version) \
            .execute()

        if existing_version.data:
            logs.error(f"Version {request.version} already exists for platform {request.platform}")
            error_response = ErrorResponse(
                status="VERSION_ALREADY_EXISTS",
                detail=f"版本 {request.version} 已存在"
            )

            return ResponseJson(
                code=200,
                message="error",
                data=error_response
            )

        # 2. 插入新版本
        new_version = {
            'platform': request.platform,
            'version': request.version,
            'update_url': request.update_url,
            'release_notes': request.release_notes,
            'status': request.status,
            'is_force_update': request.is_force_update
        }

        response = supabase.table('client_versions') \
            .insert(new_version) \
            .execute()

        if not response.data:
            logs.error("Failed to insert new version")
            error_response = ErrorResponse(
                status="VERSION_INSERT_FAILED",
                detail="添加新版本失败"
            )
            return ResponseJson(
                code=200,
                message="error",
                data=error_response
            )

        logs.info(f"Successfully added new version: {response.data[0]}")
        return ResponseJson(
            code=200,
            message="success",
            data=response.data[0]
        )

    except HTTPException:
        raise
    except Exception as e:
        error_message = str(e)
        if hasattr(e, 'json'):
            error_data = e.json()
            if isinstance(error_data, dict):
                error_message = error_data.get('message', str(e))

        error_response = ErrorResponse(
            status="VERSION_ADD_FAILED",
            detail=f"添加客户端版本失败: {error_message}"
        )
        return ResponseJson(
            code=200,
            message="error",
            data=error_response
        )


@router.get("/tt", response_model=None)
async def get_tt():
    return ResponseModel(
        message="获取用户信息成功",
        data={"user": {
            "id": "123",
            "email": "<EMAIL>"
        }}
    )


@router.get("/list-files", response_model=None)
async def list_files(
        platform: Optional[str] = None,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100)
):
    """
    列出可下载的文件

    Args:
        platform: 平台筛选（可选）
        page: 页码，从1开始
        page_size: 每页数量

    Returns:
        文件列表响应
    """
    try:
        # 构建查询
        query = supabase.table('files').select('*', count='exact').eq('status', 1)

        # 如果指定了平台，添加平台筛选
        if platform:
            query = query.eq('platform', platform)

        # 计算分页
        start = (page - 1) * page_size
        end = start + page_size - 1

        # 执行查询
        response = query.order('created_at', desc=True).range(start, end).execute()

        if not response.data and page > 1:
            # 如果请求的页码超出范围，返回第一页
            start = 0
            end = page_size - 1
            response = query.order('created_at', desc=True).range(start, end).execute()
            page = 1

        # 转换为模型
        files = []
        for item in response.data:
            files.append(FileInfo(
                id=item['id'],
                filename=item['filename'],
                file_size=item['file_size'],
                file_type=item['file_type'],
                description=item.get('description'),
                version=item.get('version'),
                platform=item.get('platform'),
                download_count=item['download_count'],
                created_at=item['created_at']
            ))

        # 获取总数
        total = response.count if response.count is not None else len(files)

        result = FileListResponse(
            files=files,
            total=total,
            page=page,
            page_size=page_size
        )

        return ResponseJson(
            code=200,
            message="success",
            data=result
        )
    except Exception as e:
        logs.error(f"Failed to list files: {str(e)}", exc_info=True)
        error_response = ErrorResponse(
            status="LIST_FILES_FAILED",
            detail="获取文件列表失败"
        )
        return ResponseJson(
            code=200,
            message="error",
            data=error_response
        )


@router.get("/download/{file_id}", response_model=None)
async def download_file(
        file_id: str,
        request: Request
):
    """
    下载文件接口

    Args:
        file_id: 文件ID
        request: 请求对象，用于获取Range头信息

    Returns:
        文件流响应
    """
    try:
        # 1. 从数据库获取文件信息
        file_info_response = supabase.table('files') \
            .select('*') \
            .eq('id', file_id) \
            .eq('status', 1) \
            .execute()

        if not file_info_response.data:
            logs.error(f"File not found or not available: {file_id}")
            raise HTTPException(status_code=404, detail="文件不存在或不可用")

        file_info = file_info_response.data[0]
        object_key = file_info['file_path']  # OSS对象键

        # 2. 获取文件元数据
        try:
            metadata = oss_client.get_file_metadata(object_key)
            file_size = metadata['content_length']
        except Exception as e:
            logs.error(f"Failed to get file metadata from OSS: {str(e)}")
            raise HTTPException(status_code=500, detail="获取文件信息失败")

        # 3. 处理断点续传
        range_header = request.headers.get('range')
        start = 0
        end = file_size - 1

        if range_header:
            try:
                range_type, range_value = range_header.split('=')
                if range_type.strip() == 'bytes':
                    start_str, end_str = range_value.split('-')
                    start = int(start_str) if start_str else 0
                    end = int(end_str) if end_str else file_size - 1
            except ValueError:
                logs.warning(f"Invalid range header: {range_header}")

        # 限制范围
        if end >= file_size:
            end = file_size - 1

        # 4. 设置响应头
        headers = {
            'Accept-Ranges': 'bytes',
            'Content-Length': str(end - start + 1),
            'Content-Range': f'bytes {start}-{end}/{file_size}',
            'Content-Type': metadata['content_type'],
            'Content-Disposition': f'attachment; filename="{file_info["filename"]}"'
        }

        # 5. 创建文件流
        async def file_stream():
            try:
                # 获取文件流
                file_obj = oss_client.get_file_stream(object_key)

                # 如果请求的是部分内容，需要跳过前面的字节
                if start > 0:
                    file_obj.read(start)

                # 读取请求的字节范围
                remaining = end - start + 1
                chunk_size = 8192 * 1024  # 8MB chunks

                while remaining > 0:
                    chunk = file_obj.read(min(chunk_size, remaining))
                    if not chunk:
                        break
                    remaining -= len(chunk)
                    yield chunk

                file_obj.close()
            except Exception as e:
                logs.error(f"Error streaming file from OSS: {str(e)}")
                raise HTTPException(status_code=500, detail="文件流读取失败")

        # 6. 更新下载计数
        if start == 0:  # 只有当开始下载时才增加计数
            try:
                supabase.table('files') \
                    .update({'download_count': file_info['download_count'] + 1}) \
                    .eq('id', file_id) \
                    .execute()
            except Exception as e:
                logs.warning(f"Failed to update download count: {str(e)}")

        logs.info(f"Starting file download: {file_id}, range: {start}-{end}, size: {end - start + 1} bytes")
        return StreamingResponse(
            file_stream(),
            headers=headers,
            status_code=206 if range_header else 200
        )

    except HTTPException:
        raise
    except Exception as e:
        logs.error(f"Failed to download file: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="文件下载失败")


@router.post("/upload-file", response_model=None)
async def upload_file():
    """
    上传文件接口（管理员使用）

    注意：这个接口需要在前端实现上传功能
    """
    # 这里将来实现文件上传功能
    pass


@router.get("/latest-file/{platform}", response_model=None)
async def get_latest_file(
        platform: str,
        request: Request
):
    """
    获取指定平台的最新版本文件并下载

    Args:
        platform: 平台，如 windows, macos 等
        request: 请求对象

    Returns:
        文件流响应
    """
    try:
        # # 1. 查询指定平台的最新文件
        # latest_file_response = supabase.table('files') \
        #     .select('*') \
        #     .eq('platform', platform) \
        #     .eq('status', 1) \
        #     .order('created_at', desc=True) \
        #     .limit(1) \
        #     .execute()
        #
        # if not latest_file_response.data:
        #     logs.error(f"No latest file found for platform: {platform}")
        #     raise HTTPException(status_code=404, detail=f"未找到{platform}平台的最新文件")
        #
        # file_info = latest_file_response.data[0]
        # file_id = file_info['id']

        # 2. 调用下载接口
        logs.info('get_latest_file')
        return await download_file('1', request)

    except HTTPException:
        raise
    except Exception as e:
        logs.error(f"Failed to get latest file for platform {platform}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取{platform}平台最新文件失败")



@router.get("/link", response_model=None)
async def get_download_link():
    success, url, error = oss_client.get_latest_download_url()
    if success:
        return ResponseJson(
            code =200,
            message="success",
            data={"url":url}
        )
    else:
        ResponseJson(
            code=400,
            message="error",
            data={"error": error}
        )
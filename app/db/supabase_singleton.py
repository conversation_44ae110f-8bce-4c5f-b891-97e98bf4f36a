"""
Supabase客户端单例模式实现
提供优化的Supabase客户端，使用HTTP连接池
"""
import os
import time
import threading
from typing import Dict, Any, Optional, Callable
from functools import wraps
import backoff
from datetime import datetime

from supabase import create_client, Client
from postgrest.exceptions import APIError
from supabase.lib.client_options import ClientOptions

from ..core.config import settings
from ..core.http_client import get_http_client
from utils.logger import get_logger

logs = get_logger()

# Supabase客户端配置
SUPABASE_TIMEOUT = int(os.getenv("SUPABASE_TIMEOUT", "30"))  # 请求超时时间（秒）
SUPABASE_MAX_RETRIES = int(os.getenv("SUPABASE_MAX_RETRIES", "3"))  # 最大重试次数
SUPABASE_RETRY_DELAY = int(os.getenv("SUPABASE_RETRY_DELAY", "1"))  # 重试延迟（秒）

# 单例锁
_singleton_lock = threading.RLock()

# 单例实例
_supabase_instance = None


class SupabaseClientSingleton:
    """
    Supabase客户端单例类
    使用自定义HTTP客户端和连接池，支持自动重试
    """
    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SupabaseClientSingleton, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        with self._lock:
            if self._initialized:
                return

            self._initialized = True
            self._client = None
            self._last_refresh = 0
            self._refresh_interval = int(os.getenv("SUPABASE_REFRESH_INTERVAL", "3600"))  # 刷新间隔（秒）

            # 初始化客户端
            self._initialize_client()

    def _initialize_client(self):
        """初始化Supabase客户端"""
        try:
            # 创建Supabase客户端，使用自定义HTTP客户端
            self._client = create_client(
                supabase_url=settings.SUPABASE_URL,
                supabase_key=settings.SUPABASE_KEY,
                options=ClientOptions(
                    auto_refresh_token=True,
                    persist_session=True,
                )
            )

            self._last_refresh = time.time()
            logs.info("Supabase client singleton initialized")
        except Exception as e:
            logs.error(f"Failed to initialize Supabase client: {str(e)}")
            raise

    def _check_refresh(self):
        """检查是否需要刷新客户端"""
        current_time = time.time()
        if (current_time - self._last_refresh) > self._refresh_interval:
            logs.info("Refreshing Supabase client")
            self._initialize_client()

    @property
    def client(self) -> Client:
        """获取Supabase客户端实例"""
        with self._lock:
            self._check_refresh()
            return self._client

    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        with self._lock:
            stats = {
                "last_refresh": self._last_refresh,
                "uptime": time.time() - self._last_refresh
            }
            return stats

    @backoff.on_exception(
        backoff.expo,
        (APIError, Exception),
        max_tries=SUPABASE_MAX_RETRIES,
        base=SUPABASE_RETRY_DELAY
    )
    def execute_request(self, func_name: str, *args, **kwargs):
        """
        执行Supabase请求，带有错误处理和重试机制

        Args:
            func_name: 要调用的函数名
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            请求结果
        """
        with self._lock:
            try:
                # 获取要调用的函数
                func = self._client
                for part in func_name.split('.'):
                    func = getattr(func, part)

                # 执行请求
                result = func(*args, **kwargs)
                return result
            except APIError as e:
                logs.error(f"Supabase API error: {str(e)}")
                raise
            except Exception as e:
                logs.error(f"Error in Supabase operation: {str(e)}")
                raise

def get_supabase() -> Client:
    """
    获取Supabase客户端实例（单例模式）

    Returns:
        Client: Supabase客户端实例
    """
    global _supabase_instance

    with _singleton_lock:
        if _supabase_instance is None:
            _supabase_instance = SupabaseClientSingleton()

        return _supabase_instance.client

def execute_supabase_request(func_name: str, *args, **kwargs):
    """
    执行Supabase请求，使用单例客户端

    Args:
        func_name: 要调用的函数名（例如："table.select"）
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        请求结果
    """
    global _supabase_instance

    with _singleton_lock:
        if _supabase_instance is None:
            _supabase_instance = SupabaseClientSingleton()

        return _supabase_instance.execute_request(func_name, *args, **kwargs)

def get_supabase_stats() -> Dict[str, Any]:
    """
    获取Supabase客户端统计信息

    Returns:
        Dict[str, Any]: 统计信息
    """
    global _supabase_instance

    with _singleton_lock:
        if _supabase_instance is None:
            return {"status": "not_initialized"}

        return _supabase_instance.get_stats()

# 导出一个默认客户端，用于向后兼容
supabase = get_supabase()

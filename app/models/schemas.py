from enum import Enum, StrEnum
from typing import Optional, Dict, Any, TypeVar, Generic, List
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime

DataT = TypeVar('DataT')


# Auth相关模型
class UserSignUp(BaseModel):
    email: str
    password: str


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class UserLogin(BaseModel):
    email: str
    password: str


class ResetPasswordRequest(BaseModel):
    email: str
    verification_code: str | None = None
    new_password: str | None = None


# 代币消耗模型
class UseFeatureRequest(BaseModel):
    feature_key: str = Field(..., min_length=1, max_length=50)  # 功能id
    token_cost: int = Field(..., gt=0)  # 消耗情况


# 代币充值模型
class RechargeRequest(BaseModel):
    racharge_type: int = Field(..., gt=0)  # 充值类型，0:赠送，1:付费充值
    token_add: int = Field(..., gt=0, le=10000)  # 限制充值金额范围


class TokenTransactionResponse(BaseModel):
    transaction_id: int  # 改为整数类型
    order_id: str  # 业务流水号
    balance_after: int
    amount: int
    description: str
    created_at: Optional[int] = None  # 修改为毫秒时间戳整数类型


class TokenResponse(BaseModel):
    transaction_id: int
    order_id: str
    balance_after: int
    amount: int
    description: str
    created_at: Optional[int] = None  # 修改为毫秒时间戳整数类型


# 响应模型
class ResponseModel(BaseModel, Generic[DataT]):
    message: str
    data: Optional[DataT] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    status: str
    detail: str


class ResponseJson(BaseModel, Generic[DataT]):
    code: int
    message: str
    data: Optional[DataT] = None


class BalanceResponse(BaseModel):
    balance: int


class TransactionPrefix(str, Enum):
    """交易流水号前缀枚举"""
    TRADE = "TT"  # 消费交易
    RECHARGE = "RC"  # 充值交易


# 算力充值汇率模型

class TokenExchangeRate(BaseModel):
    id: str
    price: int  # 单位：分
    token_amount: int
    status: int = 1
    sort_order: int = 0
    description: Optional[str] = None
    created_at: Optional[int] = None
    updated_at: Optional[int] = None


# 充值套餐模型，包含价格和算力数量
class RechargePackage(BaseModel):
    price: int  # 单位：分
    token_amount: int


class TokenExchangeRateResponse(BaseModel):
    message: str
    data: list[TokenExchangeRate]


class RechargePackageResponse(BaseModel):
    message: str
    data: list[RechargePackage]


# 代币消耗系数模型
class TokenCoefficient(BaseModel):
    id: str
    coefficient_key: str
    name: str
    coefficient_value: float
    coefficient_type: int
    description: Optional[str] = None
    status: int = 1
    created_at: Optional[int] = None
    updated_at: Optional[int] = None


class TokenCoefficientResponse(BaseModel):
    message: str
    data: list[TokenCoefficient]


# 简化的代币消耗系数模型
class TokenCoefficientSimple(BaseModel):
    coefficient_key: str
    coefficient_value: float


class TokenCoefficientSimpleResponse(BaseModel):
    message: str
    data: list[TokenCoefficientSimple]


# 客户端版本相关模型
class ClientPlatform(StrEnum):
    """客户端平台枚举"""
    ANDROID = "android"
    IOS = "ios"
    WINDOWS = "windows"
    MACOS = "macos"
    WEB = "web"


class ClientVersionCheckRequest(BaseModel):
    """客户端版本检查请求"""
    platform: ClientPlatform
    current_version: str


class VersionCheckResult(BaseModel):
    """版本检查结果"""
    is_latest: bool
    need_update: bool
    force_update: bool
    latest_version: str
    update_url: str
    release_notes: str


class ClientVersionAddRequest(BaseModel):
    """新增版本请求"""
    platform: ClientPlatform
    version: str  #版本号
    update_url: str  #下载地址
    release_notes: str  #更新说明
    status: int  #状态
    is_force_update: bool  #是否强制更新


# 文件相关模型
class FileInfo(BaseModel):
    """文件信息"""
    id: str
    filename: str
    file_size: int
    file_type: str
    description: Optional[str] = None
    version: Optional[str] = None
    platform: Optional[str] = None
    download_count: int
    created_at: int


class FileListResponse(BaseModel):
    """文件列表响应"""
    files: List[FileInfo]
    total: int
    page: int
    page_size: int

from fastapi import HTTPException

from app.db.supabase_singleton import supabase



version_data = {
  "platform": "windows",
  "version": "1.2.3",
  "update_url": "https://example.com/download/windows/1.2.0",
  "release_notes": "1. 修复了一些bug",
  "is_force_update": False
}

a = supabase.table('client_versions') \
            .insert(version_data) \
            .execute()
print(a.data)

# response = supabase.table('token_transactions') \
#     .insert({
#         "user_id": "99a9e85e-0bbb-4d77-96b8-937e4c5d633c",
#         "amount": -50,
#         "balance_after": 120,
#         "description": "使用xx功能",
#         "transaction_type": 1,
#         "created_at": 1741397207670,
#         "order_id": "TT20250308092645961696",
#         "feature_key": "xx",
#         "feature_count": 1
#     }) \
#     .execute()
# print(response.data)

# b = supabase.table('client_versions') \
#             .select("*") \
#             .execute()
# print(b.data)

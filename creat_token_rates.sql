-- 创建算力充值对应表
CREATE TABLE public.token_exchange_rates (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    price integer NOT NULL,  -- 充值金额，单位：分
    token_amount integer NOT NULL,  -- 获得的算力数量
    status smallint NOT NULL DEFAULT 1,  -- 1:启用，2:禁用
    sort_order integer DEFAULT 0,  -- 排序顺序
    description text,  -- 简短描述（可选）
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,

    -- 约束条件
    CONSTRAINT positive_price CHECK (price > 0),
    CONSTRAINT positive_token_amount CHECK (token_amount > 0),
    CONSTRAINT valid_status CHECK (status IN (1, 2))
);

-- 设置 RLS
ALTER TABLE public.token_exchange_rates ENABLE ROW LEVEL SECURITY;

-- 允许服务角色进行所有操作
CREATE POLICY "Service role can do everything"
ON public.token_exchange_rates
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 允许所有用户查看汇率
CREATE POLICY "Everyone can view exchange rates"
ON public.token_exchange_rates
FOR SELECT
TO authenticated, anon
USING (status = 1);  -- 只显示启用的汇率

-- 创建触发器来自动更新 updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.token_exchange_rates
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- 插入一些示例数据
INSERT INTO public.token_exchange_rates
    (price, token_amount, sort_order, description)
VALUES
    (100, 10, 1, '10元 = 10算力'),
    (500, 55, 2, '50元 = 55算力'),
    (1000, 120, 3, '100元 = 120算力'),
    (2000, 260, 4, '200元 = 260算力'),
    (5000, 700, 5, '500元 = 700算力'),
    (10000, 1500, 6, '1000元 = 1500算力');

-- 授予必要的权限
GRANT USAGE ON SCHEMA public TO service_role, authenticated, anon;
GRANT ALL ON public.token_exchange_rates TO service_role;
GRANT SELECT ON public.token_exchange_rates TO authenticated, anon;
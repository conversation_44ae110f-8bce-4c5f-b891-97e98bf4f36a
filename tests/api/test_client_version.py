"""
测试客户端版本检查API
"""
import pytest
from unittest.mock import patch, MagicMock

from app.api.client import check_client_version
from app.models.schemas import ClientVersionCheckRequest, ClientPlatform


@pytest.fixture
def mock_platform_config_response():
    """模拟平台配置响应"""
    return MagicMock(
        data=[
            {
                'platform': 'android',
                'min_version': '0.9.0',
                'latest_version': '1.1.0',
                'global_force_update': False
            }
        ]
    )

@pytest.fixture
def mock_latest_version_response():
    """模拟最新版本响应"""
    return MagicMock(
        data=[
            {
                'platform': 'android',
                'version': '1.1.0',
                'status': 1,  # 最新版本
                'is_force_update': False,
                'update_url': 'https://example.com/download/android',
                'release_notes': '新版本修复了一些bug，提升了性能'
            }
        ]
    )


@pytest.mark.asyncio
async def test_check_version_needs_update(mock_platform_config_response, mock_latest_version_response):
    """测试需要更新的情况"""
    # 准备请求数据
    request = ClientVersionCheckRequest(
        platform=ClientPlatform.ANDROID,
        current_version='1.0.0'
    )

    # 模拟Supabase响应
    with patch('app.api.client.supabase.table') as mock_table:
        # 设置模拟行为
        def side_effect(table_name):
            mock_select = MagicMock()
            mock_eq = MagicMock()

            if table_name == 'platform_configs':
                mock_eq.execute.return_value = mock_platform_config_response
            elif table_name == 'client_versions':
                mock_eq2 = MagicMock()
                mock_eq.eq.return_value = mock_eq2
                mock_eq2.execute.return_value = mock_latest_version_response

            mock_select.eq.return_value = mock_eq
            return mock_select

        mock_table.side_effect = lambda table_name: side_effect(table_name)

        # 调用API
        response = await check_client_version(request)

        # 验证结果
        assert response.message == "success"
        assert response.data.is_latest is False
        assert response.data.need_update is True
        assert response.data.force_update is False
        assert response.data.latest_version == "1.1.0"
        assert response.data.update_url == "https://example.com/download/android"


@pytest.mark.asyncio
async def test_check_version_force_update(mock_platform_config_response, mock_latest_version_response):
    """测试强制更新的情况"""
    # 准备请求数据
    request = ClientVersionCheckRequest(
        platform=ClientPlatform.ANDROID,
        current_version='0.8.0'  # 低于最低版本
    )

    # 模拟Supabase响应
    with patch('app.api.client.supabase.table') as mock_table:
        # 设置模拟行为
        def side_effect(table_name):
            mock_select = MagicMock()
            mock_eq = MagicMock()

            if table_name == 'platform_configs':
                mock_eq.execute.return_value = mock_platform_config_response
            elif table_name == 'client_versions':
                mock_eq2 = MagicMock()
                mock_eq.eq.return_value = mock_eq2
                mock_eq2.execute.return_value = mock_latest_version_response

            mock_select.eq.return_value = mock_eq
            return mock_select

        mock_table.side_effect = lambda table_name: side_effect(table_name)

        # 调用API
        response = await check_client_version(request)

        # 验证结果
        assert response.message == "success"
        assert response.data.is_latest is False
        assert response.data.need_update is True
        assert response.data.force_update is True  # 强制更新
        assert response.data.latest_version == "1.1.0"


@pytest.mark.asyncio
async def test_check_version_latest(mock_platform_config_response, mock_latest_version_response):
    """测试已是最新版本的情况"""
    # 准备请求数据
    request = ClientVersionCheckRequest(
        platform=ClientPlatform.ANDROID,
        current_version='1.1.0'  # 与最新版本相同
    )

    # 模拟Supabase响应
    with patch('app.api.client.supabase.table') as mock_table:
        # 设置模拟行为
        def side_effect(table_name):
            mock_select = MagicMock()
            mock_eq = MagicMock()

            if table_name == 'platform_configs':
                mock_eq.execute.return_value = mock_platform_config_response
            elif table_name == 'client_versions':
                mock_eq2 = MagicMock()
                mock_eq.eq.return_value = mock_eq2
                mock_eq2.execute.return_value = mock_latest_version_response

            mock_select.eq.return_value = mock_eq
            return mock_select

        mock_table.side_effect = lambda table_name: side_effect(table_name)

        # 调用API
        response = await check_client_version(request)

        # 验证结果
        assert response.message == "success"
        assert response.data.is_latest is True
        assert response.data.need_update is False
        assert response.data.force_update is False
        assert response.data.latest_version == "1.1.0"

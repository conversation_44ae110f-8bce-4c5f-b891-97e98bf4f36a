from unittest.mock import patch, MagicMock

import pytest
from fastapi import HTTPException

from app.api.transactions import get_balance
from app.models.schemas import ResponseModel

pytestmark = pytest.mark.asyncio  # 标记所有测试为异步


# 模拟用户数据
@pytest.fixture
def mock_user():
    return MagicMock(id="test_id")


# 模拟空ID用户
@pytest.fixture
def mock_invalid_user():
    return MagicMock(id=None)


# 模拟数据库响应
@pytest.fixture
def mock_db_response():
    return MagicMock(data=[{"balance": 100}])


# 模拟空数据响应
@pytest.fixture
def mock_empty_response():
    return MagicMock(data=[])


async def test_get_balance_success(mock_user, mock_db_response):
    """测试成功获取用户余额"""
    with patch('app.api.transactions.supabase') as mock_supabase:
        # 设置模拟响应
        mock_select = MagicMock()
        mock_select.eq.return_value.execute.return_value = mock_db_response
        mock_table = MagicMock()
        mock_table.select.return_value = mock_select
        mock_supabase.table.return_value = mock_table

        # 调用接口
        response = await get_balance(mock_user)

        # 验证响应
        assert isinstance(response, ResponseModel)
        assert response.message == "success"
        assert response.data["balance"] == 100

        # 验证调用链
        mock_supabase.table.assert_called_once_with("user_tokens")
        mock_table.select.assert_called_once_with("balance")
        mock_select.eq.assert_called_once_with("user_id", "test_id")


async def test_get_balance_user_not_exists(mock_invalid_user):
    """测试用户不存在的情况"""
    with patch('app.api.transactions.supabase') as mock_supabase:
        # 验证异常
        with pytest.raises(HTTPException) as exc_info:
            await get_balance(mock_invalid_user)

        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户不存在"

        # 验证数据库没有被调用
        mock_supabase.table.assert_not_called()


async def test_get_balance_db_error(mock_user):
    """测试数据库错误的情况"""
    with patch('app.api.transactions.supabase') as mock_supabase:
        # 设置模拟响应
        mock_select = MagicMock()
        mock_select.eq.return_value.execute.side_effect = Exception("Database error")
        mock_table = MagicMock()
        mock_table.select.return_value = mock_select
        mock_supabase.table.return_value = mock_table

        # 验证异常
        with pytest.raises(HTTPException) as exc_info:
            await get_balance(mock_user)

        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "获取余额失败"


async def test_get_balance_response_format(mock_user, mock_db_response):
    """测试响应格式是否符合预期"""
    with patch('app.api.transactions.supabase') as mock_supabase:
        # 设置模拟响应
        mock_select = MagicMock()
        mock_select.eq.return_value.execute.return_value = mock_db_response
        mock_table = MagicMock()
        mock_table.select.return_value = mock_select
        mock_supabase.table.return_value = mock_table

        response = await get_balance(mock_user)

        # 验证响应格式
        assert isinstance(response, ResponseModel)
        assert isinstance(response.data, dict)
        assert isinstance(response.data["balance"], int)
        assert response.message == "success"

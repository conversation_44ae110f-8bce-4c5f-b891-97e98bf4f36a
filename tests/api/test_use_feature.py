import pytest
import time
from unittest.mock import patch
from unittest.mock import AsyncMock, MagicMock
import httpx
import json

from xff.xf1 import UseFeatureRequest, use_feature
from app.core.security import generate_signature


@pytest.fixture
def mock_response():
    return {
        "message": "success",
        "data": {
            "transaction_id": "123e4567-e89b-12d3-a456-426614174000",
            "balance_after": 90,
            "amount": 10,
            "description": "使用chat功能"
        }
    }

@pytest.fixture
def valid_request():
    return UseFeatureRequest(feature_key="chat", token_cost=10)

@pytest.fixture
def valid_token():
    return "valid_token"

@pytest.fixture
def mock_async_client():
    mock_client = AsyncMock()
    mock_client.__aenter__.return_value = mock_client
    mock_client.__aexit__.return_value = None
    return mock_client

@pytest.mark.asyncio
async def test_use_feature_success(mock_response, valid_request, valid_token, mock_async_client):
    """测试正常使用功能的情况"""
    # 配置响应
    response_mock = AsyncMock()
    response_mock.json.return_value = mock_response
    response_mock.raise_for_status.return_value = None
    mock_async_client.post.return_value = response_mock

    with patch('httpx.AsyncClient', return_value=mock_async_client):
        result = await use_feature(valid_request, valid_token)
        
        # 验证返回结果
        assert result["message"] == "success"
        assert "transaction_id" in result["data"]
        assert result["data"]["amount"] == 10
        assert result["data"]["balance_after"] == 90
        
        # 验证请求参数
        call_args = mock_async_client.post.call_args
        assert call_args is not None
        args, kwargs = call_args
        
        # 验证URL
        assert args[0] == "http://localhost:8000/api/transactions/use"
        
        # 验证headers
        assert kwargs["headers"]["Authorization"] == f"Bearer {valid_token}"
        assert kwargs["headers"]["Content-Type"] == "application/json"
        
        # 验证请求体
        assert kwargs["json"] == {"feature_key": "chat", "token_cost": 10}
        
        # 验证签名参数
        params = kwargs["params"]
        assert "timestamp" in params
        assert "sign" in params

@pytest.mark.asyncio
async def test_use_feature_insufficient_balance(valid_request, valid_token, mock_async_client):
    """测试余额不足的情况"""
    error_response = {
        "detail": "余额不足"
    }
    
    response_mock = AsyncMock()
    response_mock.status_code = 400
    response_mock.json.return_value = error_response
    response_mock.text = json.dumps(error_response)
    response_mock.raise_for_status.side_effect = httpx.HTTPStatusError(
        "400 Bad Request", 
        request=MagicMock(), 
        response=response_mock
    )
    mock_async_client.post.return_value = response_mock

    with patch('httpx.AsyncClient', return_value=mock_async_client):
        with pytest.raises(httpx.HTTPStatusError) as exc_info:
            await use_feature(valid_request, valid_token)
        
        assert exc_info.value.response.status_code == 400
        assert json.loads(exc_info.value.response.text)["detail"] == "余额不足"

@pytest.mark.asyncio
async def test_use_feature_invalid_signature(valid_request, valid_token, mock_async_client):
    """测试签名验证失败的情况"""
    error_response = {
        "detail": "签名验证失败"
    }
    
    response_mock = AsyncMock()
    response_mock.status_code = 400
    response_mock.json.return_value = error_response
    response_mock.text = json.dumps(error_response)
    response_mock.raise_for_status.side_effect = httpx.HTTPStatusError(
        "400 Bad Request", 
        request=MagicMock(), 
        response=response_mock
    )
    mock_async_client.post.return_value = response_mock

    with patch('httpx.AsyncClient', return_value=mock_async_client):
        with pytest.raises(httpx.HTTPStatusError) as exc_info:
            await use_feature(valid_request, valid_token)
        
        assert exc_info.value.response.status_code == 400
        assert json.loads(exc_info.value.response.text)["detail"] == "签名验证失败"

@pytest.mark.asyncio
async def test_use_feature_expired_request(valid_request, valid_token, mock_async_client):
    """测试请求过期的情况"""
    error_response = {
        "detail": "请求已过期"
    }
    
    response_mock = AsyncMock()
    response_mock.status_code = 400
    response_mock.json.return_value = error_response
    response_mock.text = json.dumps(error_response)
    response_mock.raise_for_status.side_effect = httpx.HTTPStatusError(
        "400 Bad Request", 
        request=MagicMock(), 
        response=response_mock
    )
    mock_async_client.post.return_value = response_mock

    with patch('httpx.AsyncClient', return_value=mock_async_client):
        with pytest.raises(httpx.HTTPStatusError) as exc_info:
            await use_feature(valid_request, valid_token)
        
        assert exc_info.value.response.status_code == 400
        assert json.loads(exc_info.value.response.text)["detail"] == "请求已过期"

@pytest.mark.asyncio
async def test_use_feature_network_error(valid_request, valid_token):
    """测试网络错误的情况"""
    with patch('httpx.AsyncClient', side_effect=httpx.NetworkError("连接失败")):
        with pytest.raises(Exception) as exc_info:
            await use_feature(valid_request, valid_token)
        assert str(exc_info.value) == "连接失败"

@pytest.mark.asyncio
async def test_use_feature_invalid_token_cost(valid_token, mock_async_client):
    """测试无效的代币消耗值"""
    invalid_request = UseFeatureRequest(feature_key="chat", token_cost=-10)
    error_response = {
        "detail": "无效的代币消耗"
    }
    
    response_mock = AsyncMock()
    response_mock.status_code = 400
    response_mock.json.return_value = error_response
    response_mock.text = json.dumps(error_response)
    response_mock.raise_for_status.side_effect = httpx.HTTPStatusError(
        "400 Bad Request", 
        request=MagicMock(), 
        response=response_mock
    )
    mock_async_client.post.return_value = response_mock

    with patch('httpx.AsyncClient', return_value=mock_async_client):
        with pytest.raises(httpx.HTTPStatusError) as exc_info:
            await use_feature(invalid_request, valid_token)
        
        assert exc_info.value.response.status_code == 400
        assert json.loads(exc_info.value.response.text)["detail"] == "无效的代币消耗" 
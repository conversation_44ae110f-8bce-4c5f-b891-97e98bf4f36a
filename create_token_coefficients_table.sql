-- 创建代币消耗系数表
CREATE TABLE public.token_coefficients (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    coefficient_key text NOT NULL UNIQUE,  -- 系数标识符，例如：'asr_qps', 'trans_qps'
    name text NOT NULL,  -- 系数名称，例如：'asr任务', 'trans任务'
    coefficient_value numeric(10, 4) NOT NULL,  -- 系数值，例如：1.5表示增加50%，0.8表示减少20%
    coefficient_type smallint NOT NULL,  -- 系数类型：1=模型系数，2=用户等级系数，3=时间系数，4=其他
    description text,  -- 系数描述
    status smallint NOT NULL DEFAULT 1,  -- 状态：1启用，2禁用
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,

    -- 确保系数值为正数
    CONSTRAINT positive_coefficient CHECK (coefficient_value > 0),
    -- 确保状态值有效
    CONSTRAINT valid_status CHECK (status IN (1, 2)),
    -- 确保系数类型有效
    CONSTRAINT valid_coefficient_type CHECK (coefficient_type BETWEEN 1 AND 4)
);

-- 设置 RLS
ALTER TABLE public.token_coefficients ENABLE ROW LEVEL SECURITY;

-- 允许服务角色进行所有操作
CREATE POLICY "Service role can do everything"
ON public.token_coefficients
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 允许所有用户查看系数配置
CREATE POLICY "Everyone can view coefficients"
ON public.token_coefficients
FOR SELECT
TO authenticated, anon
USING (status = 1);  -- 只显示启用的系数

-- 创建触发器函数来自动更新 updated_at（如果不存在）
DO $do$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_updated_at') THEN
        CREATE OR REPLACE FUNCTION public.handle_updated_at()
        RETURNS trigger AS $func$
        BEGIN
            NEW.updated_at = (extract(epoch from now()) * 1000)::BIGINT;
            RETURN NEW;
        END;
        $func$ LANGUAGE plpgsql SECURITY DEFINER;
    END IF;
END
$do$;

-- 创建触发器来自动更新 updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.token_coefficients
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- 插入一些初始数据
INSERT INTO public.token_coefficients
    (coefficient_key, name, coefficient_value, coefficient_type, description, status)
VALUES
    ('model_gpt4', 'GPT-4模型', 2.0, 1, 'GPT-4模型的代币消耗系数，基础消耗的2倍', 1),
    ('model_gpt35', 'GPT-3.5模型', 1.0, 1, 'GPT-3.5模型的代币消耗系数，基础消耗', 1),
    ('user_vip', 'VIP用户', 0.8, 2, 'VIP用户的代币消耗系数，基础消耗的80%', 1),
    ('user_svip', '超级VIP用户', 0.5, 2, '超级VIP用户的代币消耗系数，基础消耗的50%', 1),
    ('time_peak', '高峰时段', 1.2, 3, '高峰时段的代币消耗系数，基础消耗的120%', 1),
    ('time_off_peak', '低峰时段', 0.9, 3, '低峰时段的代币消耗系数，基础消耗的90%', 1);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_token_coefficients_type
    ON public.token_coefficients(coefficient_type)
    WHERE status = 1;

-- 授予必要的权限
GRANT USAGE ON SCHEMA public TO service_role, authenticated, anon;
GRANT ALL ON public.token_coefficients TO service_role;
GRANT SELECT ON public.token_coefficients TO authenticated, anon;

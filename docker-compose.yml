version: '3.8'

services:
  api:
    build: .
    container_name: tianluo_api
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DEBUG=${DEBUG:-False}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

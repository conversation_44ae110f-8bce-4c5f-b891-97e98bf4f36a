name: 构建并发布Docker镜像

on:
  push:
    branches: [ "master", "feat/1fast1.0" ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ "master", "feat/fast1.0" ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: 'arm64,amd64'

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到GitHub容器仓库
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short

      - name: 构建并推送Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 输出镜像信息
        if: github.event_name != 'pull_request'
        run: |
          echo "\n\n镜像构建完成并已推送到GitHub Container Registry"
          echo "\n镜像地址："
          echo "${{ steps.meta.outputs.tags }}" | tr '\n' '\n'
          echo "\n\n访问镜像： https://github.com/${{ github.repository }}/pkgs/container/${{ github.event.repository.name }}"
          echo "\n拉取镜像示例： docker pull $(echo "${{ steps.meta.outputs.tags }}" | head -n 1)"

      # 包保持私有状态，需要认证才能访问

import httpx
import json
from typing import Dict, Any, Optional, Tuple, Union


def check_client_version(
        api_url: str,
        platform: str,
        current_version: str
) -> Tuple[bool, Dict[str, Any]]:
    """
    检查客户端版本是否需要更新

    Args:
        api_url: API基础URL，例如 "https://api.example.com"
        platform: 客户端平台，例如 "android", "ios", "windows"
        current_version: 当前客户端版本，例如 "1.0.0"

    Returns:
        (成功标志, 结果数据) 元组:
        - 成功时返回 (True, 版本信息字典)
        - 失败时返回 (False, 错误信息字典)
    """
    # 构建请求URL
    check_endpoint = f"{api_url.rstrip('/')}/api/client/check-version"

    # 准备请求头和请求体
    headers = {
        "Content-Type": "application/json"
    }

    json_data = {
        "platform": platform,
        "current_version": current_version
    }
    try:
        # 发送POST请求
        response = httpx.post(
            check_endpoint,
            headers=headers,
            json=json_data,
            timeout=10  # 10秒超时
        )

        # 检查HTTP状态码
        response.raise_for_status()

        # 解析响应
        result = response.json()

        # 验证响应格式
        if "message" not in result or "data" not in result:
            return False, {"error": "Invalid response format"}

        # 检查是否有错误消息
        if result["message"] == "error":
            error_code = result["data"].get("code", "UNKNOWN_ERROR")
            error_detail = result["data"].get("detail", "Unknown error")
            return False, {"error_code": error_code, "error_detail": error_detail}

        # 返回成功结果
        return result

    except json.JSONDecodeError:
        # 处理JSON解析错误
        return False, {"error_code": "INVALID_JSON", "error_detail": "Invalid JSON response"}
    except Exception as e:
        # 处理其他错误
        return False, {"error_code": "UNEXPECTED_ERROR", "error_detail": str(e)}


def handle_version_result(success: bool, result: Dict[str, Any]) -> None:
    """
    处理版本检查结果

    Args:
        success: 是否成功
        result: 结果数据
    """
    if not success:
        # 处理错误情况
        error_code = result.get("error_code", "UNKNOWN_ERROR")
        error_detail = result.get("error_detail", "未知错误")

        print(f"\n版本检查失败: [{error_code}] {error_detail}")

        # 根据错误代码处理不同情况
        if error_code == "PLATFORM_CONFIG_NOT_FOUND":
            print("提示: 您的平台可能不受支持，请联系客服")
        elif error_code == "VERSION_NOT_FOUND":
            print("提示: 未找到版本信息，请稍后再试")
        elif error_code == "REQUEST_FAILED":
            print("提示: 网络连接失败，请检查网络设置")

        return
    print(result)
    # 处理成功情况
    if result.get("need_update", False):
        latest_version = result.get("latest_version", "未知")
        update_url = result.get("update_url", "")
        release_notes = result.get("release_notes", "")
        is_force = result.get("force_update", False)


        if is_force:
            print("【强制更新】必须更新才能继续使用应用")
        else:
            print("【可选更新】建议更新到最新版本")
    else:
        print("\n当前已是最新版本")


# 使用示例
if __name__ == "__main__":
    # 配置参数
    API_URL = "http://127.0.0.1:8000"  # 替换为实际的API地址
    PLATFORM = "windows33"  # 平台: android, ios, windows, macos, web
    CURRENT_VERSION = "2.1.0"  # 当前版本号

    print(f"检查版本更新中... (当前版本: {CURRENT_VERSION})")

    # 调用版本检查接口
    result = check_client_version(API_URL, PLATFORM, CURRENT_VERSION)
    print(result)

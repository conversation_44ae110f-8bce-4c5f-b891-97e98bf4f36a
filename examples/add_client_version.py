"""
添加客户端版本示例代码
"""
import httpx
import json

# API 基础URL
BASE_URL = "http://localhost:8000"  # 根据实际部署环境修改
token = "eyJhbGciOiJIUzI1NiIsImtpZCI6Ik0wTUYvMFIwemkzVDBWUUMiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xJxA_6xxTerLfGFx79C2-T-HpzBw9APXhMRwMSOIg6A"
def add_client_version():
    # 请求URL
    url = f"{BASE_URL}/api/client/add-version"
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # 请求体
    payload = {
        "platform": "windows",  # 平台：android, ios, windows, macos, web
        "version": "4.0.0",     # 版本号
        "update_url": "https://example.com/download/windows/1.0.0",  # 更新包下载地址
        "release_notes": "1. 新增功能A\n2. 优化功能B\n3. 修复问题C",  # 更新说明
        "status": 1,            # 状态：1=最新版本, 2=历史版本, 3=测试版本, 4=已废弃
        "is_force_update": False  # 是否强制更新
    }
    
    try:
        # 发送POST请求
        response = httpx.post(url, headers=headers, json=payload)
        
        # 解析响应
        result = response.json()
        print(result)


    except json.JSONDecodeError as e:
        print(f"解析响应JSON失败: {str(e)}")
    except Exception as e:
        print(f"发生未知错误: {str(e)}")

if __name__ == "__main__":
    add_client_version() 
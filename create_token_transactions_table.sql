------------------------------------------
-- 1. 序列创建
------------------------------------------
CREATE SEQUENCE token_transactions_id_seq
    INCREMENT BY 1
    MINVALUE 1
    NO MAXVALUE
    START WITH 1
    CACHE 1;

------------------------------------------
-- 2. 表创建
------------------------------------------
CREATE TABLE public.token_transactions (
    -- 主键和基础字段
    id bigint NOT NULL DEFAULT nextval('token_transactions_id_seq') PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) NOT NULL,
    order_id varchar(64) UNIQUE,  -- 业务流水号

    -- 交易相关字段
    amount integer NOT NULL,  -- 正数表示收入，负数表示支出
    balance_after integer NOT NULL,  -- 交易后的余额
    description text NOT NULL,  -- 交易描述
    transaction_type smallint NOT NULL,  -- 交易类型（1:消费, 2:充值, 3:赠送, 4:其他）

    -- 功能相关字段
    feature_key varchar(50),
    feature_count integer CHECK (feature_count > 0),

    -- 时间戳
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,

    -- 约束条件
    CONSTRAINT positive_balance_after CHECK (balance_after >= 0),
    CONSTRAINT valid_transaction_type CHECK (transaction_type BETWEEN 1 AND 4)
);

------------------------------------------
-- 3. 安全策略设置
------------------------------------------
-- 启用行级安全
ALTER TABLE public.token_transactions ENABLE ROW LEVEL SECURITY;

-- 服务角色策略
CREATE POLICY "Service role can do everything"
    ON public.token_transactions
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- 用户查看策略
CREATE POLICY "Users can view own transactions"
    ON public.token_transactions
    FOR SELECT
    USING (auth.uid() = user_id);

------------------------------------------
-- 4. 功能使用存储过程
------------------------------------------
CREATE OR REPLACE FUNCTION public.use_feature_transaction(
    p_user_id uuid,
    p_amount integer,
    p_description text,
    p_transaction_type smallint,
    p_order_id varchar(64),
    p_feature_key varchar(50),
    p_feature_count integer
)
RETURNS jsonb AS $$
DECLARE
    v_current_balance integer;
    v_new_balance integer;
    v_transaction_id bigint;
    v_created_at BIGINT;
    v_result jsonb;
BEGIN
    -- 获取并锁定当前余额
    SELECT balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_user_id
    FOR UPDATE;

    -- 检查用户是否存在
    IF v_current_balance IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '用户代币信息不存在',
            'code', 'USER_NOT_FOUND'
        );
    END IF;

    -- 计算并验证新余额
    v_new_balance := v_current_balance + p_amount;
    IF v_new_balance < 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '余额不足',
            'code', 'INSUFFICIENT_BALANCE'
        );
    END IF;

    -- 更新用户余额
    UPDATE public.user_tokens
    SET balance = v_new_balance
    WHERE user_id = p_user_id;

    -- 记录交易
    INSERT INTO public.token_transactions
        (user_id, amount, balance_after, description, transaction_type,
         order_id, feature_key, feature_count)
    VALUES
        (p_user_id, p_amount, v_new_balance, p_description, p_transaction_type,
         p_order_id, p_feature_key, p_feature_count)
    RETURNING id, created_at INTO v_transaction_id, v_created_at;

    -- 返回成功结果
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'transaction_id', v_transaction_id,
            'order_id', p_order_id,
            'balance_after', v_new_balance,
            'amount', p_amount,
            'description', p_description,
            'created_at', v_created_at
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

------------------------------------------
-- 5. 充值存储过程
------------------------------------------
CREATE OR REPLACE FUNCTION public.record_token_transaction(
    p_user_id uuid,           -- 用户ID
    p_amount integer,         -- 充值金额（正数）
    p_description text,       -- 交易描述
    p_transaction_type smallint,  -- 交易类型（2表示充值）
    p_order_id varchar(64)    -- 订单号
)
RETURNS jsonb AS $$
DECLARE
    v_current_balance integer;
    v_new_balance integer;
    v_transaction_id bigint;
    v_created_at BIGINT;  -- 修改为 BIGINT 类型
    v_result jsonb;
BEGIN
    -- 参数验证
    IF p_amount <= 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '充值金额必须大于0',
            'code', 'INVALID_AMOUNT'
        );
    END IF;

    IF p_transaction_type != 2 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '无效的交易类型',
            'code', 'INVALID_TRANSACTION_TYPE'
        );
    END IF;

    -- 检查订单重复
    IF EXISTS (SELECT 1 FROM public.token_transactions WHERE order_id = p_order_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '订单已存在',
            'code', 'DUPLICATE_ORDER'
        );
    END IF;

    -- 获取并锁定当前余额
    SELECT balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_user_id
    FOR UPDATE;

    -- 处理新用户情况
    IF v_current_balance IS NULL THEN
        INSERT INTO public.user_tokens (user_id, balance)
        VALUES (p_user_id, 0)
        RETURNING balance INTO v_current_balance;
    END IF;

    -- 更新余额
    v_new_balance := v_current_balance + p_amount;
    UPDATE public.user_tokens
    SET balance = v_new_balance
    WHERE user_id = p_user_id;

    -- 记录交易
    INSERT INTO public.token_transactions
        (user_id, amount, balance_after, description, transaction_type, order_id)
    VALUES
        (p_user_id, p_amount, v_new_balance, p_description, p_transaction_type, p_order_id)
    RETURNING id, created_at INTO v_transaction_id, v_created_at;

    -- 返回结果，保持created_at的原始格式
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'transaction_id', v_transaction_id,
            'order_id', p_order_id,
            'balance_after', v_new_balance,
            'amount', p_amount,
            'description', p_description,
            'created_at', to_char(v_created_at, 'YYYY-MM-DD"T"HH24:MI:SS.US"+00:00"')
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

------------------------------------------
-- 6. 权限设置和索引创建
------------------------------------------
-- 授予存储过程执行权限
GRANT EXECUTE ON FUNCTION public.use_feature_transaction TO service_role;
GRANT EXECUTE ON FUNCTION public.record_token_transaction TO service_role;

-- 授予架构和表权限
GRANT USAGE ON SCHEMA public TO service_role, authenticated, anon;
GRANT ALL ON public.token_transactions TO service_role;
GRANT SELECT ON public.token_transactions TO authenticated;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_token_transactions_feature_key
    ON token_transactions(feature_key)
    WHERE feature_key IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_token_transactions_order_id
    ON public.token_transactions(order_id)
    WHERE order_id IS NOT NULL;
# encrypt_credentials.py - 仅在开发环境使用，不包含在客户端发布版本中
import base64
import os
import platform
import sys
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


def get_machine_info():
    """获取机器特定信息"""
    return (
            os.name.encode() +  # 操作系统类型
            platform.system().encode() +  # 操作系统名称
            platform.node().encode() +  # 网络名称
            platform.machine().encode()  # 机器类型
    )


def derive_key(salt, app_info):
    """从应用程序信息派生加密密钥"""
    # 使用应用程序信息和机器特定信息生成密钥
    machine_info = get_machine_info()

    # 使用PBKDF2派生密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )

    key = base64.urlsafe_b64encode(kdf.derive(app_info + machine_info))
    return key


def encrypt_credentials(aki, aks, asr_api_key):
    """加密凭证并生成代码片段"""
    # 应用程序特定的信息
    salt = b'lin_trans_application_salt'
    app_info = b'aliyun_credentials_v1'

    # 派生密钥
    key = derive_key(salt, app_info)
    cipher = Fernet(key)

    # 加密凭证
    encrypted_aki = cipher.encrypt(aki.encode())
    encrypted_aks = cipher.encrypt(aks.encode())
    encrypted_asr_api_key = cipher.encrypt(asr_api_key.encode())

    # 生成代码片段
    code = f"""
# 加密的凭证 - 由开发环境工具生成
# 这些值是加密的，可以安全地嵌入到代码中
ENCRYPTED_AKI = {encrypted_aki}
ENCRYPTED_AKS = {encrypted_aks}
ENCRYPTED_ASR_API_KEY = {encrypted_asr_api_key}

# 用于解密的盐值（可以是公开的）
SALT = {salt}

# 应用程序特定的信息（可以是公开的）
APP_INFO = {app_info}
"""

    return code


def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("用法: python encrypt_credentials.py <AccessKey ID> <AccessKey Secret> <ASR API Key>")
        return

    aki = "LTAI5t7eCsZFb4AnqJFX5e3v"
    aks = "******************************"
    asr_api_key = "sk-b1d261afb71d40bea90b61ac11a202af"

    code = encrypt_credentials(aki, aks, asr_api_key)

    # 输出到文件
    with open("encrypted_credentials.py", "w") as f:
        f.write(code)

    print("加密凭证已生成并保存到 encrypted_credentials.py")
    print("请将该文件中的内容复制到您的应用程序代码中")


if __name__ == "__main__":
    main()
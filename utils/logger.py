import sys
from datetime import datetime
from pathlib import Path

from loguru import logger

# 全局logger实例
_logger = None


def get_logger():
    """
    获取或创建logger实例
    """
    global _logger

    if _logger is None:
        _logger = setup_logger()

    return _logger


def setup_logger():
    """
    配置 loguru logger
    """
    # 创建日志目录
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)

    # 生成日志文件名
    log_file = log_dir / f"{datetime.now().strftime('%Y-%m-%d')}.log"

    # 移除默认的 sink
    logger.remove()

    # 添加控制台输出
    logger.add(sys.stdout, level="INFO", colorize=True)

    # 添加文件输出
    logger.add(str(log_file), rotation="00:00",  # 每天午夜轮换
               retention="1 days",  # 保留30天
               encoding="utf-8")

    return logger

"""
版本比较工具
"""
from typing import List, <PERSON><PERSON>


def parse_version(version: str) -> List[int]:
    """
    解析版本号字符串为整数列表
    
    Args:
        version: 版本号字符串，如 "1.2.3"
        
    Returns:
        整数列表，如 [1, 2, 3]
    """
    # 处理可能的预发布版本标识符（如 -alpha, -beta 等）
    if "-" in version:
        version = version.split("-")[0]
        
    # 处理可能的构建元数据（如 +build123）
    if "+" in version:
        version = version.split("+")[0]
    
    # 将版本号分割并转换为整数列表
    try:
        return [int(x) for x in version.split(".")]
    except ValueError:
        # 如果转换失败，返回 [0]
        return [0]


def compare_versions(version1: str, version2: str) -> int:
    """
    比较两个版本号
    
    Args:
        version1: 第一个版本号
        version2: 第二个版本号
        
    Returns:
        -1 如果 version1 < version2
        0 如果 version1 == version2
        1 如果 version1 > version2
    """
    v1_parts = parse_version(version1)
    v2_parts = parse_version(version2)
    
    # 确保两个列表长度相同
    max_length = max(len(v1_parts), len(v2_parts))
    v1_parts.extend([0] * (max_length - len(v1_parts)))
    v2_parts.extend([0] * (max_length - len(v2_parts)))
    
    # 逐位比较
    for i in range(max_length):
        if v1_parts[i] < v2_parts[i]:
            return -1
        elif v1_parts[i] > v2_parts[i]:
            return 1
            
    # 所有位都相同
    return 0

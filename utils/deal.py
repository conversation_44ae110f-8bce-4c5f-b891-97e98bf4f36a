from datetime import datetime
import random
import string

from app.models.schemas import TransactionPrefix


def generate_transaction_no(prefix: TransactionPrefix = TransactionPrefix.TRADE) -> str:
    """
    生成交易流水号
    格式：前缀(TT/RC) + 年月日(8位) + 时分秒(6位) + 随机数(6位)
    用作 order_id

    Args:
        prefix (TransactionPrefix): 交易流水号前缀，只能是 TransactionPrefix 枚举中的值，默认为 TT

    Returns:
        str: 生成的交易流水号
    """
    # 获取当前时间
    now = datetime.now()
    date_str = now.strftime("%Y%m%d")
    time_str = now.strftime("%H%M%S")

    # 生成6位随机数
    random_str = ''.join(random.choices(string.digits, k=6))

    # 组合流水号
    return f"{prefix.value}{date_str}{time_str}{random_str}"
-- 创建用户代币表
create table public.user_tokens (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users(id) not null unique,
    balance integer default 0 not null,
    created_at BIGINT default (extract(epoch from now()) * 1000)::BIGINT not null,
    updated_at BIGINT default (extract(epoch from now()) * 1000)::BIGINT not null,

    -- 确保余额不能为负数
    constraint positive_balance check (balance >= 0)
);

-- 设置 RLS (Row Level Security) 策略
alter table public.user_tokens enable row level security;

-- 修改策略：允许服务角色进行所有操作
create policy "Service role can do everything"
on public.user_tokens
for all
to service_role
using (true)
with check (true);

-- 创建策略：用户只能查看自己的代币余额
create policy "Users can view own token balance"
on public.user_tokens
for select
using (auth.uid() = user_id);

-- 创建触发器函数来自动更新 updated_at
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = (extract(epoch from now()) * 1000)::BIGINT;
    return new;
end;
$$ language plpgsql security definer;

-- 创建触发器
create trigger set_updated_at
    before update on public.user_tokens
    for each row
    execute function public.handle_updated_at();

-- 创建函数用于新用户注册时自动创建代币记录
create or replace function public.handle_new_user()
returns trigger as $$
begin
    insert into public.user_tokens (user_id, balance)
    values (new.id, 100); -- 给新用户默认100个代币
    return new;
end;
$$ language plpgsql security definer;

-- 创建触发器，在新用户注册时自动创建代币记录
create trigger on_auth_user_created
    after insert on auth.users
    for each row
    execute function public.handle_new_user();

-- 创建函数用于用户删除时自动删除代币记录
create or replace function public.handle_user_deletion()
returns trigger as $$
begin
    delete from public.user_tokens where user_id = old.id;
    return old;
end;
$$ language plpgsql security definer;

-- 创建触发器，在用户被删除时自动删除代币记录
create trigger on_auth_user_deleted
    before delete on auth.users
    for each row
    execute function public.handle_user_deletion();

-- 授予必要的权限
grant usage on schema public to service_role, authenticated, anon;
grant all on public.user_tokens to service_role;
grant select on public.user_tokens to authenticated;
# 生产环境部署指南

本指南提供了将天罗API项目部署到生产环境的最佳实践。

## 架构概述

在生产环境中，我们采用以下架构：

```
客户端 -> Nginx (80/443端口) -> FastAPI应用 (8000端口)
```

- **Nginx** 作为反向代理，处理SSL终止、请求缓冲和基本安全防护
- **FastAPI应用** 在内部网络中运行，不直接暴露给外部

## 部署步骤

### 1. 准备环境变量

创建一个`.env`文件，包含必要的环境变量：

```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
DEBUG=False
```

### 2. SSL证书（推荐）

对于HTTPS支持，您需要准备SSL证书：

```bash
# 创建SSL证书目录
mkdir -p nginx/ssl

# 将您的证书和密钥放入此目录
# cert.pem - SSL证书
# key.pem - SSL私钥
```

如果您没有SSL证书，可以使用Let's Encrypt免费获取：

```bash
# 安装certbot
apt-get update
apt-get install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d yourdomain.com
```

### 3. 修改Nginx配置

编辑`nginx/conf.d/app.conf`文件，取消注释HTTPS部分，并将`server_name`替换为您的域名。

### 4. 启动服务

使用docker-compose启动服务：

```bash
docker-compose up -d
```

## 安全最佳实践

1. **始终使用HTTPS**：在生产环境中，所有HTTP流量应重定向到HTTPS。

2. **定期更新依赖**：定期检查并更新依赖项，以修复安全漏洞。

3. **限制容器权限**：容器应以非root用户运行。可以在Dockerfile中添加：
   ```dockerfile
   # 创建非root用户
   RUN adduser --disabled-password --gecos '' appuser
   USER appuser
   ```

4. **设置适当的资源限制**：在docker-compose.yml中添加资源限制：
   ```yaml
   services:
     api:
       # ...其他配置...
       deploy:
         resources:
           limits:
             cpus: '1'
             memory: 1G
   ```

5. **启用日志轮换**：防止日志文件占用过多磁盘空间：
   ```yaml
   services:
     api:
       # ...其他配置...
       logging:
         driver: "json-file"
         options:
           max-size: "10m"
           max-file: "3"
   ```

## 监控和维护

1. **设置健康检查**：在docker-compose.yml中添加健康检查：
   ```yaml
   services:
     api:
       # ...其他配置...
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
         interval: 30s
         timeout: 10s
         retries: 3
   ```

2. **设置自动重启**：确保容器在崩溃时自动重启：
   ```yaml
   services:
     api:
       # ...其他配置...
       restart: unless-stopped
   ```

3. **备份数据**：定期备份重要数据。

## 故障排除

1. **查看容器日志**：
   ```bash
   docker-compose logs api
   ```

2. **检查容器状态**：
   ```bash
   docker-compose ps
   ```

3. **进入容器内部**：
   ```bash
   docker-compose exec api bash
   ```

4. **重启服务**：
   ```bash
   docker-compose restart api
   ```

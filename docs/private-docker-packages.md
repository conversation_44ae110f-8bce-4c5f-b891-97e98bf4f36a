# 使用私有Docker包指南

本文档提供了如何访问、拉取和使用GitHub Container Registry (GHCR)中私有Docker镜像的详细说明。

## 目录

- [认证方法](#认证方法)
- [本地开发环境](#本地开发环境)
- [CI/CD环境](#cicd环境)
- [服务器部署](#服务器部署)
- [Kubernetes部署](#kubernetes部署)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 认证方法

要访问私有Docker包，您需要先向GitHub Container Registry进行认证。

### 创建个人访问令牌(PAT)

1. 访问GitHub设置：https://github.com/settings/tokens
2. 点击"Generate new token (classic)"
3. 为令牌提供一个描述性名称，如"Docker Registry Access"
4. 选择以下权限：
   - `read:packages` (如果只需要拉取镜像)
   - `write:packages` (如果需要推送镜像)
   - `delete:packages` (如果需要删除镜像，可选)
5. 点击"Generate token"
6. **重要**：复制并安全保存生成的令牌，它只会显示一次

## 本地开发环境

### 使用Docker CLI登录

```bash
# 方法1：交互式登录
docker login ghcr.io -u YOUR_GITHUB_USERNAME

# 方法2：使用管道传递令牌（避免在命令历史中保存令牌）
echo YOUR_PERSONAL_ACCESS_TOKEN | docker login ghcr.io -u YOUR_GITHUB_USERNAME --password-stdin
echo **************************************** | docker login ghcr.io -u riftcover --password-stdin
```

### 拉取私有镜像

登录后，您可以像拉取公共镜像一样拉取私有镜像：

```bash
# 拉取特定标签
docker pull ghcr.io/riftcover/tianluo_api:master

# 拉取特定版本
docker pull ghcr.io/riftcover/tianluo_api:v1.0.0
```

```
docker run -p 8000:8000 -e SUPABASE_URL=https://efhtgyfhwaxqequunbhc.supabase.co -e SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVmaHRneWZod2F4cWVxdXVuYmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk0MzMxMTMsImV4cCI6MjA1NTAwOTExM30.5jsufdamloTbAO19QPEk7MhqdTdUSFooS13hdZSruyA -e DEBUG=True ghcr.io/riftcover/tianluo_api:master
```

### 在docker-compose中使用

如果您使用docker-compose，可以先登录，然后正常使用私有镜像：

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    image: ghcr.io/riftcover/tianluo_api:master
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DEBUG=${DEBUG:-False}
```

## CI/CD环境

### GitHub Actions

在GitHub Actions中使用私有包非常简单，因为它自动提供了必要的认证：

```yaml
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 登录到GitHub容器仓库
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 拉取并运行镜像
        run: |
          docker pull ghcr.io/riftcover/tianluo_api:master
          docker run -d -p 8000:8000 ghcr.io/riftcover/tianluo_api:master
```

### GitLab CI/CD

在GitLab CI/CD中，您需要设置认证信息：

```yaml
variables:
  DOCKER_AUTH_CONFIG: '{"auths":{"ghcr.io":{"auth":"'$GHCR_AUTH'"}}}'

stages:
  - deploy

deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker pull ghcr.io/riftcover/tianluo_api:master
    - docker run -d -p 8000:8000 ghcr.io/riftcover/tianluo_api:master
```

在GitLab项目设置中，添加一个名为`GHCR_AUTH`的变量，其值为`base64(username:token)`。

### Jenkins

在Jenkins中，您可以使用凭据绑定：

```groovy
pipeline {
    agent any
    
    environment {
        DOCKER_REGISTRY_CREDENTIALS = credentials('github-container-registry')
    }
    
    stages {
        stage('Deploy') {
            steps {
                sh '''
                echo $DOCKER_REGISTRY_CREDENTIALS_PSW | docker login ghcr.io -u $DOCKER_REGISTRY_CREDENTIALS_USR --password-stdin
                docker pull ghcr.io/riftcover/tianluo_api:master
                docker run -d -p 8000:8000 ghcr.io/riftcover/tianluo_api:master
                '''
            }
        }
    }
}
```

## 服务器部署

### 使用Docker Compose

1. 在服务器上登录到GitHub Container Registry：

```bash
echo YOUR_PERSONAL_ACCESS_TOKEN | docker login ghcr.io -u YOUR_GITHUB_USERNAME --password-stdin
```

2. 创建docker-compose.yml文件：

```yaml
version: '3.8'

services:
  api:
    image: ghcr.io/riftcover/tianluo_api:master
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=your_supabase_url
      - SUPABASE_KEY=your_supabase_key
    volumes:
      - ./logs:/app/logs
```

3. 启动服务：

```bash
docker-compose up -d
```

### 使用Systemd服务

1. 登录到GitHub Container Registry
2. 创建systemd服务文件：

```ini
# /etc/systemd/system/tianluo-api.service
[Unit]
Description=Tianluo API Service
After=docker.service
Requires=docker.service

[Service]
TimeoutStartSec=0
Restart=always
ExecStartPre=-/usr/bin/docker stop tianluo_api
ExecStartPre=-/usr/bin/docker rm tianluo_api
ExecStart=/usr/bin/docker run --name tianluo_api \
  -p 8000:8000 \
  -e SUPABASE_URL=your_supabase_url \
  -e SUPABASE_KEY=your_supabase_key \
  -v /path/to/logs:/app/logs \
  ghcr.io/riftcover/tianluo_api:master

[Install]
WantedBy=multi-user.target
```

3. 启用并启动服务：

```bash
sudo systemctl enable tianluo-api
sudo systemctl start tianluo-api
```

## Kubernetes部署

### 创建镜像拉取密钥

```bash
kubectl create secret docker-registry ghcr-secret \
  --docker-server=ghcr.io \
  --docker-username=YOUR_GITHUB_USERNAME \
  --docker-password=YOUR_PERSONAL_ACCESS_TOKEN \
  --namespace=your-namespace
```

### 在部署中使用密钥

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tianluo-api
  namespace: your-namespace
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tianluo-api
  template:
    metadata:
      labels:
        app: tianluo-api
    spec:
      imagePullSecrets:
      - name: ghcr-secret
      containers:
      - name: api
        image: ghcr.io/riftcover/tianluo_api:master
        ports:
        - containerPort: 8000
        env:
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: tianluo-secrets
              key: supabase-url
        - name: SUPABASE_KEY
          valueFrom:
            secretKeyRef:
              name: tianluo-secrets
              key: supabase-key
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
```

## 常见问题

### 认证错误

如果遇到`unauthorized: authentication required`错误：

1. 确认您已正确登录：`docker login ghcr.io`
2. 检查您的PAT是否有效且具有正确的权限
3. 确认PAT没有过期
4. 检查您是否有权访问该包

### 找不到镜像

如果遇到`manifest for ghcr.io/riftcover/tianluo_api:master not found`错误：

1. 检查镜像名称和标签是否正确
2. 确认镜像确实存在于仓库中
3. 验证您有权访问该镜像

### 架构不匹配

如果遇到`no matching manifest for linux/arm64/v8 in the manifest list entries`错误：

1. 确认镜像支持您的系统架构
2. 如果使用M1/M2 Mac，尝试使用`--platform linux/amd64`选项：
   ```bash
   docker run --platform linux/amd64 ghcr.io/riftcover/tianluo_api:master
   ```

## 最佳实践

1. **使用特定版本标签**：避免使用`latest`或`master`标签，而是使用特定版本（如`v1.0.0`）
2. **定期轮换PAT**：每3-6个月更新一次个人访问令牌
3. **最小权限原则**：只授予必要的最小权限
4. **使用环境变量**：通过环境变量传递敏感信息，而不是硬编码
5. **配置自动化部署**：设置CI/CD流水线自动部署新版本
6. **监控和日志**：设置适当的监控和日志记录，以便快速诊断问题

---

如有任何问题或需要进一步的帮助，请联系项目维护者。

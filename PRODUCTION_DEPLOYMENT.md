# 天螺生产环境部署指南

## 概述

本文档介绍如何使用 Docker Compose 在生产环境中部署天螺项目。

## 系统架构

- **前端 (tianluo)**: Next.js 应用，运行在端口 3000
- **后端 (tianluo_api)**: FastAPI 应用，运行在端口 8000
- **Nginx**: 反向代理，处理 SSL 终止和负载均衡
- **可选服务**: Redis 缓存、PostgreSQL 数据库

## 部署前准备

### 1. 环境配置

复制环境变量示例文件并配置：

```bash
cp env.production.example .env.production
```

编辑 `.env.production` 文件，设置以下变量：

- `SUPABASE_URL`: Supabase 项目 URL
- `SUPABASE_KEY`: Supabase 匿名密钥
- `CORS_ORIGINS`: 允许的跨域源
- `NEXT_PUBLIC_API_URL`: 前端 API 基础 URL

### 2. SSL 证书（推荐）

如果使用 HTTPS，将 SSL 证书放置在 `nginx/ssl/` 目录：

```bash
mkdir -p nginx/ssl
# 将证书文件放置在此目录
# cert.pem - 证书文件
# key.pem - 私钥文件
```

然后在 `nginx/nginx.conf` 中取消注释 HTTPS 服务器配置块。

## 部署步骤

### 1. 构建和启动服务

```bash
# 使用生产配置启动所有服务
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 2. 验证部署

- 前端: http://localhost:3000 (直接访问) 或 http://localhost (通过 Nginx)
- 后端 API: http://localhost:8000
- 健康检查: http://localhost/health

### 3. 服务管理

```bash
# 停止服务
docker-compose -f docker-compose.prod.yml down

# 重启特定服务
docker-compose -f docker-compose.prod.yml restart api

# 查看服务日志
docker-compose -f docker-compose.prod.yml logs api

# 更新服务
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --no-deps --build <service_name>
```

## 服务配置说明

### API 服务
- 容器名称: `tianluo_api`
- 端口: 8000
- 健康检查: Python requests 调用
- 日志轮转: 10MB/文件，保留 3 个文件
- 重启策略: `unless-stopped`

### Web 服务
- 容器名称: `tianluo_web`
- 端口: 3000
- 健康检查: Node.js HTTP 请求
- 依赖: API 服务健康后启动

### Nginx 服务
- 容器名称: `tianluo_nginx`
- 端口: 80 (HTTP), 443 (HTTPS)
- 功能: 反向代理、静态文件服务、SSL 终止
- 配置文件: `nginx/nginx.conf`

## 监控和维护

### 健康检查

所有服务都配置了健康检查：
- API: 每 30 秒检查 `/health` 端点
- Web: 每 30 秒检查根路径
- Nginx: 每 30 秒检查 `/health` 端点

### 日志管理

所有服务使用 JSON 文件驱动记录日志：
- 最大文件大小: 10MB
- 保留文件数: 3 个
- 位置: `/var/lib/docker/containers/`

### 数据持久化

- API 日志: 使用 Docker 卷 `api-logs`
- 如果启用数据库: 使用 `postgres-data` 卷
- 如果启用 Redis: 使用 `redis-data` 卷

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查日志
   docker-compose -f docker-compose.prod.yml logs <service_name>
   
   # 检查配置
   docker-compose -f docker-compose.prod.yml config
   ```

2. **健康检查失败**
   ```bash
   # 进入容器检查
   docker exec -it tianluo_api bash
   docker exec -it tianluo_web sh
   ```

3. **网络问题**
   ```bash
   # 检查网络
   docker network ls
   docker network inspect tianluo_tianluo-network
   ```

### 性能优化

1. **资源限制**（可选）
   在服务配置中添加：
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '1.0'
         memory: 512M
       reservations:
         cpus: '0.5'
         memory: 256M
   ```

2. **缓存优化**
   - 启用 Redis 服务
   - 配置 Nginx 静态文件缓存
   - 使用 CDN 加速静态资源

## 安全建议

1. **防火墙配置**
   - 只开放必要的端口 (80, 443)
   - 限制管理端口访问

2. **SSL/TLS**
   - 使用有效的 SSL 证书
   - 配置 HTTP 到 HTTPS 重定向
   - 设置安全头

3. **环境变量**
   - 使用强密码
   - 定期轮换密钥
   - 不要在代码中硬编码敏感信息

4. **定期更新**
   - 更新基础镜像
   - 更新依赖包
   - 应用安全补丁 
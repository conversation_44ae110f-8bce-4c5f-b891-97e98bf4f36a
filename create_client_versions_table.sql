-- 创建客户端版本信息表
CREATE TABLE public.client_versions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    platform text NOT NULL,  -- 平台：android, ios, windows, macos, web
    version text NOT NULL,  -- 版本号
    release_date BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,  -- 发布日期
    update_url text NOT NULL,  -- 更新地址
    release_notes text NOT NULL,  -- 更新说明
    status smallint NOT NULL DEFAULT 1,  -- 状态：1=最新版本, 2=历史版本, 3=测试版本, 4=已废弃
    is_force_update boolean DEFAULT false,  -- 是否强制更新
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,

    -- 确保平台+版本组合唯一
    CONSTRAINT unique_platform_version UNIQUE (platform, version),
    -- 确保平台值有效
    CONSTRAINT valid_platform CHECK (platform IN ('android', 'ios', 'windows', 'macos', 'web')),
    -- 确保状态值有效
    CONSTRAINT valid_status CHECK (status BETWEEN 1 AND 4)
);

-- 创建平台配置表（存储每个平台的最低支持版本等信息）
CREATE TABLE public.platform_configs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    platform text NOT NULL UNIQUE,  -- 平台：android, ios, windows, macos, web
    min_version text NOT NULL,  -- 最低支持版本号
    latest_version text NOT NULL,  -- 最新版本号
    global_force_update boolean DEFAULT false,  -- 全局强制更新开关
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL,
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000)::BIGINT NOT NULL
);

-- 设置 RLS
ALTER TABLE public.client_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.platform_configs ENABLE ROW LEVEL SECURITY;

-- 允许服务角色进行所有操作
CREATE POLICY "Service role can do everything on client_versions"
ON public.client_versions
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

CREATE POLICY "Service role can do everything on platform_configs"
ON public.platform_configs
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 允许所有用户查看版本信息
CREATE POLICY "Everyone can view client versions"
ON public.client_versions
FOR SELECT
TO authenticated, anon
USING (true);

CREATE POLICY "Everyone can view platform configs"
ON public.platform_configs
FOR SELECT
TO authenticated, anon
USING (true);

-- 允许认证用户和匿名用户添加新版本
CREATE POLICY "Authenticated users can insert versions"
ON public.client_versions
FOR INSERT
TO authenticated, anon
WITH CHECK (true);

-- 允许认证用户和匿名用户插入平台配置
CREATE POLICY "Users can insert platform configs"
ON public.platform_configs
FOR INSERT
TO authenticated, anon
WITH CHECK (true);

-- 允许认证用户和匿名用户更新平台配置
CREATE POLICY "Users can update platform configs"
ON public.platform_configs
FOR UPDATE
TO authenticated, anon
USING (true)
WITH CHECK (true);



CREATE POLICY "Users can update platform configs"
ON public.client_versions
FOR UPDATE
TO authenticated, anon
USING (true)
WITH CHECK (true);

-- 创建触发器来自动更新 updated_at
CREATE TRIGGER set_client_versions_updated_at
    BEFORE UPDATE ON public.client_versions
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_platform_configs_updated_at
    BEFORE UPDATE ON public.platform_configs
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- 创建函数：当插入新版本时自动更新平台配置表的最新版本
CREATE OR REPLACE FUNCTION update_latest_version()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果新插入的版本状态为"最新版本"(1)，则更新平台配置表
    IF NEW.status = 1 THEN
        -- 将该平台的所有其他版本状态更新为历史版本(2)
        UPDATE public.client_versions
        SET status = 2,  -- 历史版本
            updated_at = (extract(epoch from now()) * 1000)::BIGINT
        WHERE platform = NEW.platform
          AND id != NEW.id
          AND status = 1;  -- 只更新之前的最新版本
          
        -- 使用 UPSERT 语法更新或插入平台配置
        INSERT INTO public.platform_configs
            (platform, min_version, latest_version, updated_at)
        VALUES
            (NEW.platform, NEW.version, NEW.version, (extract(epoch from now()) * 1000)::BIGINT)
        ON CONFLICT (platform) 
        DO UPDATE SET
            latest_version = NEW.version,
            updated_at = (extract(epoch from now()) * 1000)::BIGINT;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器：当插入新版本时自动更新平台配置
CREATE TRIGGER update_platform_config_on_new_version
    AFTER INSERT ON public.client_versions
    FOR EACH ROW
    EXECUTE FUNCTION update_latest_version();

-- 插入一些初始数据
INSERT INTO public.client_versions
    (platform, version, update_url, release_notes, status)
VALUES
    ('windows', '0.2.1', 'https://example.com/download/windows/1.0.0', '初始版本', 1),
    ('macos', '0.2.1', 'https://example.com/download/macos/1.0.0', '初始版本', 1),
    ('web', '0.2.1', 'https://example.com/download/web/1.0.0', '初始版本', 1);

-- 授予必要的权限
GRANT USAGE ON SCHEMA public TO service_role, authenticated, anon;
GRANT ALL ON public.client_versions TO service_role;
GRANT ALL ON public.platform_configs TO service_role;
GRANT SELECT ON public.client_versions TO authenticated, anon;
GRANT SELECT ON public.platform_configs TO authenticated, anon;
GRANT UPDATE ON public.platform_configs TO authenticated,anon;
GRANT INSERT ON public.client_versions TO authenticated,anon;

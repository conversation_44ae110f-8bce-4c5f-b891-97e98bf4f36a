from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api import auth, features, transactions, users, client
from app.core.config import settings
from app.middleware.auth import auth_middleware
from app.middleware.performance import PerformanceMiddleware
from app.core.http_client import close_http_clients


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 预加载资源
    from app.core import preload

    # 应用程序启动时的操作
    yield

    # 应用程序关闭时的操作
    await close_http_clients()

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# 添加性能监控中间件
app.add_middleware(PerformanceMiddleware)

# 添加认证中间件
app.middleware("http")(auth_middleware)

# 注册路由
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["auth"])
app.include_router(features.router, prefix=f"{settings.API_V1_STR}/features", tags=["features"])
app.include_router(transactions.router, prefix=f"{settings.API_V1_STR}/transactions", tags=["transactions"])
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])
app.include_router(client.router, prefix=f"{settings.API_V1_STR}/client", tags=["client"])

@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.PROJECT_NAME}"}


# 使用 lifespan 事件处理程序替代 on_event

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

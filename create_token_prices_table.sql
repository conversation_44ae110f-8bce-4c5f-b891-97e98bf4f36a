-- 创建价格配置表
create table public.token_prices (
    id uuid default gen_random_uuid() primary key,
    feature_key text not null unique,  -- 功能标识符，例如：'chat', 'image_generation'
    name text not null,  -- 功能名称，例如：'AI对话', '图片生成'
    price integer not null,  -- 每次使用消耗的代币数量
    description text,  -- 功能描述
    status smallint not null default 1,  -- 状态：1启用，2禁用
    created_at BIGINT default (extract(epoch from now()) * 1000)::BIGINT not null,
    updated_at BIGINT default (extract(epoch from now()) * 1000)::BIGINT not null,

    -- 确保价格为正数
    constraint positive_price check (price > 0),
    -- 确保状态值有效
    constraint valid_status check (status in (1, 2))
);

-- 设置 RLS
alter table public.token_prices enable row level security;

-- 允许服务角色进行所有操作
create policy "Service role can do everything"
on public.token_prices
for all
to service_role
using (true)
with check (true);

-- 允许所有用户查看价格配置
create policy "Everyone can view prices"
on public.token_prices
for select
to authenticated, anon
using (true);

-- 创建触发器函数来自动更新 updated_at
create trigger set_updated_at
    before update on public.token_prices
    for each row
    execute function public.handle_updated_at();

-- 插入一些初始数据
insert into public.token_prices (feature_key, name, price, description, status)
values
    ('chat', 'AI对话', 1, '与AI助手进行对话，每次对话消耗1代币', 1),
    ('image_generation', '图片生成', 5, '使用AI生成图片，每张图片消耗5代币', 1);

-- 授予必要的权限
grant usage on schema public to service_role, authenticated, anon;
grant all on public.token_prices to service_role;
grant select on public.token_prices to authenticated, anon;